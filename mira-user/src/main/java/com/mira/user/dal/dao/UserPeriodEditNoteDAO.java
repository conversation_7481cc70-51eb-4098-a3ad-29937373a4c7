package com.mira.user.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.user.dal.entity.UserPeriodEditNoteEntity;
import com.mira.user.dal.mapper.UserPeriodEditNoteMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class UserPeriodEditNoteDAO extends ServiceImpl<UserPeriodEditNoteMapper, UserPeriodEditNoteEntity> {
    public List<UserPeriodEditNoteEntity> listByUserId(Long userId) {
        return list(Wrappers.<UserPeriodEditNoteEntity>lambdaQuery()
                .eq(UserPeriodEditNoteEntity::getUserId, userId)
                .orderByDesc(UserPeriodEditNoteEntity::getId));
    }

    public UserPeriodEditNoteEntity getByUserIdAndStart(Long userId, String periodStart) {
        return getOne(Wrappers.<UserPeriodEditNoteEntity>lambdaQuery()
                .eq(UserPeriodEditNoteEntity::getUserId, userId)
                .eq(UserPeriodEditNoteEntity::getPreviousStart, periodStart)
                .orderByDesc(UserPeriodEditNoteEntity::getId)
                .last("limit 1"));
    }
}
