package com.mira.user.dal.dao;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.user.dal.entity.AppUserRecentAccessEntity;
import com.mira.user.dal.mapper.AppUserRecentAccessMapper;
import org.springframework.stereotype.Repository;

/**
 * app_user_recent_access DAO
 *
 * <AUTHOR>
 */
@Repository
public class AppUserRecentAccessDAO extends ServiceImpl<AppUserRecentAccessMapper, AppUserRecentAccessEntity> {
    public AppUserRecentAccessEntity getByUserId(Long userId) {
        return lambdaQuery().eq(AppUserRecentAccessEntity::getUserId, userId).one();
    }
}
