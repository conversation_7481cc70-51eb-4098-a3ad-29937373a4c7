package com.mira.user.dal.dao;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.user.dal.entity.AppAccessLogEntity;
import com.mira.user.dal.mapper.AppAccessLogMapper;
import org.springframework.stereotype.Repository;

/**
 * app_access_log DAO
 *
 * <AUTHOR>
 */
@Repository
public class AppAccessLogDAO extends ServiceImpl<AppAccessLogMapper, AppAccessLogEntity> {
    public AppAccessLogEntity getByUserId(Long userId) {
        return lambdaQuery().eq(AppAccessLogEntity::getUserId, userId).one();
    }
}
