package com.mira.user.handler.chart.hormone;

import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.cycle.TestDataDTO;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.NumberFormatUtil;
import com.mira.user.controller.vo.cycle.CycleDataVO;
import com.mira.user.controller.vo.cycle.TestDataVO;
import com.mira.user.dto.common.ManualHormoneDTO;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * HCG_Qualitative
 *
 * <AUTHOR>
 */
@Component
public class HcgQualitativeHormoneHandler extends AbstractChartHormoneHandler {
    @PostConstruct
    public void init() {
        ChartHormoneHandler.set(WandTypeEnum.HCG_QUALITATIVE.getInteger(), this);
    }

    @Override
    public void handle(HormoneDTO hormoneDTO, CycleDataVO cycleDataVO, ManualHormoneDTO manualHormoneDTO) {
        String userType = ContextHolder.get(HeaderConst.USER_TYPE);
        UserTypeEnum userTypeEnum = UserTypeEnum.get(userType);
        if (hormoneDTO != null) {
            // 诊所显示 hcg 数据
            if (userTypeEnum == UserTypeEnum.CLINIC_USER) {
                TestDataVO hcgTestDataDTO = new TestDataVO();
                hcgTestDataDTO.setTestTime(hormoneDTO.getTest_time());
                hcgTestDataDTO.setValue(NumberFormatUtil.format(hormoneDTO.getTest_results().getValue1()));
                cycleDataVO.getHcgDatas().add(hcgTestDataDTO);
                cycleDataVO.getHcg2Datas().add(getHcgQualitative(hormoneDTO));
            } else {
                cycleDataVO.getHcg2Datas().add(getHcgQualitative(hormoneDTO));
            }
        }
        if (manualHormoneDTO != null) {
            // 诊所显示 hcg 数据
            if (userTypeEnum == UserTypeEnum.CLINIC_USER) {
                TestDataVO hcgTestDataDTO = new TestDataVO();
                hcgTestDataDTO.setTestTime(manualHormoneDTO.getTest_time());
                hcgTestDataDTO.setValue(NumberFormatUtil.format(manualHormoneDTO.getValue1()));
                hcgTestDataDTO.setPending(1);
                cycleDataVO.getHcgDatas().add(hcgTestDataDTO);
                cycleDataVO.getHcg2Datas().add(getHcgQualitative(hormoneDTO));
            } else {
                cycleDataVO.getHcg2Datas().add(getHcgQualitative(hormoneDTO));
            }
        }

    }

    private TestDataVO getManualHcgQualitative(ManualHormoneDTO manualHormoneDTO) {
        TestDataVO hcg2TestDataDTO = new TestDataVO();
        hcg2TestDataDTO.setTestTime(manualHormoneDTO.getTest_time());
        hcg2TestDataDTO.setValue(NumberFormatUtil.format(manualHormoneDTO.getValue1()));
        Float value1 = manualHormoneDTO.getValue1();
        Float valueResult1 = 1F;
        if (value1 < 10) {
            valueResult1 = 0F;
        }
        hcg2TestDataDTO.setValue(valueResult1);
        return hcg2TestDataDTO;
    }
}
