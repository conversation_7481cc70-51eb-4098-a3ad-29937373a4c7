package com.mira.user.enums.home;

import lombok.Getter;

/**
 * UserType Banner 枚举
 */
@Getter
public enum BannerUserTypeEnum {
    ALL("-1", "ALL"),
    CYCLE_TRACKING("0", "Cycle tracking"),
    TTA("1", "TTA-Trying to Avoid Pregnancy"),
    TTC("2", "TTC-Trying to Concieve"),
    PREGNANCY_TRACKING("3", "Pregnancy tracking"),
    OFT("4", "Ovarian function");

    private final String value;
    private final String description;

    BannerUserTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }
}
