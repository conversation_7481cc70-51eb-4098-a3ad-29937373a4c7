package com.mira.user.dto.info.v5;

import com.mira.user.enums.user.v5.MenopauseProcedureEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-06-11
 **/
@Getter
@Setter
@ApiModel("手术")
public class MenopauseProcedureDTO {
    /**
     * @see MenopauseProcedureEnum
     */
    @ApiModelProperty("procedures")
    private List<Integer> procedures;
}
