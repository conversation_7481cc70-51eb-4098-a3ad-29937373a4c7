package com.mira.user.controller.vo.menopause;

import com.mira.api.bluetooth.dto.wand.TestingWandDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-10-10
 **/
@Getter
@Setter
@ApiModel("测试计划")
public class TestingPlanVO {
    @ApiModelProperty("推荐测试的试剂及日期")
    private List<TestingWandDTO> testingWands;

    @ApiModelProperty("需要购买的测试试剂")
    private List<String> purchaseCodes;

    @ApiModelProperty("menopause报告日期")
    private List<MenopauseReportVO> menopauseReports;

    @ApiModelProperty("报告类型:null:不显示;1:menopause transition preliminary report;2:menopause transition report")
    private ReportTip reportTip;

    @ApiModelProperty("今天是否测试完成:0未完成；1已完成")
    private List<String> todayCompleted;

    @ApiModelProperty(" menopause定义Stage的种类:\n" +
            "    null或者0:无标识\n" +
            "    1:'Late reproductive age'\n" +
            "    2:'Early menopause transition'\n" +
            "    3:'Late menopause transition'\n" +
            "    4:'Menopause'\n" +
            "    5:'Early menopause'\n" +
            "    6:'remature menopause'")
    private Integer menopauseStage;



    @Getter
    @Setter
    @ApiModel("menopause报告Tip")
    public static class ReportTip {
        @ApiModelProperty("报告日期")
        private String id;

        @ApiModelProperty("报告类型:1:menopause transition preliminary report;2:menopause transition report")
        private Integer type;
    }
}
