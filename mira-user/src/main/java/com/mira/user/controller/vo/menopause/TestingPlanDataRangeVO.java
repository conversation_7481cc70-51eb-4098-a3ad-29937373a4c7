package com.mira.user.controller.vo.menopause;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-10-10
 **/
@Getter
@Setter
@ApiModel("测试计划数据范围")
public class TestingPlanDataRangeVO {
    @ApiModelProperty("数据范围")
    private List<DataRangeVO> dataRanges = new ArrayList<>();
    //是否只有FSH测试
    @ApiModelProperty("是否只有FSH测试:0否;1是")
    private Integer onlyTestFsh;
}
