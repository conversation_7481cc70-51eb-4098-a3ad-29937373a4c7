package com.mira.user.controller.vo.sys;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@ApiModel("注册用户的部分参数均值")
public class AgreementVO {
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("修改时间")
    private Date updateTime;

    @ApiModelProperty("协议名称")
    private String name;

    @ApiModelProperty("协议类型")
    private Integer type;

    @ApiModelProperty("协议标题")
    private String title;

    @ApiModelProperty("协议内容")
    private String content;
}
