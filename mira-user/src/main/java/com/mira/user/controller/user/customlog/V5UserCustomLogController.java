package com.mira.user.controller.user.customlog;

import com.mira.core.annotation.Idempotent;
import com.mira.user.controller.vo.diary.UserCustomLogUpdateVO;
import com.mira.user.dto.diary.UserCustomLogDTO;
import com.mira.user.service.user.IUserCustomLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 用户日志控制器
 *
 * <AUTHOR>
 */
@Api(tags = "06.用户日志V5")
@RestController
@RequestMapping("/app/v5/custom-log")
public class V5UserCustomLogController {
    @Resource(name = "v5UserCustomLogService")
    private IUserCustomLogService userCustomLogService;

    @Idempotent(returnValue = "diary.UserCustomLogUpdateVO")
    @ApiOperation("编辑用户Diary记录")
    @PostMapping("/update")
    public UserCustomLogUpdateVO diaryUpdate(@Valid @RequestBody UserCustomLogDTO userCustomLogDTO) {
        return userCustomLogService.diaryUpdate(userCustomLogDTO);
    }
}
