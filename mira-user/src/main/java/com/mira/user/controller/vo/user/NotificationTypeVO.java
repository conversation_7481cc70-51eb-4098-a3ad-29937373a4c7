package com.mira.user.controller.vo.user;

import com.mira.api.message.enums.NotificationAggregateTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * notification type vo
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel("消息通知类型")
public class NotificationTypeVO {
    /**
     * @see NotificationAggregateTypeEnum
     */
    @ApiModelProperty("类型值")
    private Integer type;

    @ApiModelProperty("名称")
    private String name;
}
