package com.mira.user.controller.vo.home;

import com.mira.user.controller.vo.chart.PregnantRiskVO;
import com.mira.user.controller.vo.menopause.TestingPlanVO;
import com.mira.user.enums.home.HomeActionButtonCodeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-12-13
 **/
@Getter
@Setter
@ApiModel("首页数据")
public class HomeV5CycleDataVO {
    @ApiModelProperty("上一个周期序号")
    private Integer prevCycleIndex;
    @ApiModelProperty("下一个周期序号")
    private Integer nextCycleIndex;


    @ApiModelProperty("周期编号")
    private Integer cycleIndex;

    @ApiModelProperty("用户的Mode，参考UserGoalEnum")
    private Integer userMode;

    @ApiModelProperty("是否跟踪更年期，null无标识;0 不跟踪;1跟踪")
    private Integer trackingMenopause;

    @ApiModelProperty(" menopause定义Stage的种类:\n" +
            "    null或者0:无标识\n" +
            "    1:'Late reproductive age'\n" +
            "    2:'Early menopause transition'\n" +
            "    3:'Late menopause transition'\n" +
            "    4:'Menopause'\n" +
            "    5:'Early menopause'\n" +
            "    6:'remature menopause'")
    private Integer menopauseStage;

    @ApiModelProperty("首页test plan展示结果")
    private TestingPlanVO testingPlanVO;

    @ApiModelProperty("绑定记录标示：0没有绑定过；1绑定过")
    private Integer bindLogFlag;

    @ApiModelProperty("通知标示:0 no remind;1remind")
    private Integer remindFlag = 0;


    /**
     * 以下为原 HomeDailyDataVO 中的字段
     */


    @ApiModelProperty("周期长度")
    private Integer lenCycle;

    @ApiModelProperty("周期状态，参考CycleStatusEnum")
    private Integer cycleStatus;

    @ApiModelProperty("周期开始日")
    private String datePeriodStart;

    @ApiModelProperty("经期结束日（经期不包含这天")
    private String datePeriodEnd;

    @ApiModelProperty("易孕期开始日 (由 fertility soccer 》=6 计算)")
    private String dateFwStart;

    @ApiModelProperty("易孕期结束日 （易孕期不包含这一天）")
    private String dateFwEnd;

    @ApiModelProperty("排卵日 （预测 or LH 峰值日")
    private String dateOvulation;

    @ApiModelProperty("ovulation 种类 2: detected ; 23: detected+confirmed ; 13: confirmed 1:predicted")
    private Integer ovulationType;


    @ApiModelProperty("预留，可为 null （实际测量的最高值时间）")
    private String dateLhSurge;

    @ApiModelProperty("pdG rise")
    private List<String> datePdgRises;

    @ApiModelProperty("怀孕风险预测")
    private PregnantRiskVO pregnantRisk;

    @ApiModelProperty("怀孕模式下，怀孕周期会分为3段，这个字段表示每段的长度")
    private List<Integer> lenPhase;

    @ApiModelProperty("动作按钮")
    private ActionButton actionButton;

    /**
     * @see HomeActionButtonCodeEnum
     */
    @Getter
    @Setter
    @ApiModel("动作按钮")
    public static class ActionButton {
        @ApiModelProperty("按钮上面的文字")
        private String content;
        @ApiModelProperty("按钮上面的文字的额外添加")
        private String contentAddition;
        @ApiModelProperty("按钮中间的文字")
        private String button;
        @ApiModelProperty("编码")
        private Integer code;
        @ApiModelProperty("描述（打开逻辑，开发使用，后期删除）")
        private String open;

        public ActionButton(HomeActionButtonCodeEnum homeActionButtonCodeEnum) {
            this.content = homeActionButtonCodeEnum.getContent();
            this.button = homeActionButtonCodeEnum.getButton();
            this.code = homeActionButtonCodeEnum.getCode();
            this.open = homeActionButtonCodeEnum.getOpen();
        }
    }

    @ApiModelProperty("周期数组")
    private List<Cycle> cycles;

    @Getter
    @Setter
    @ApiModel("周期")
    public static class Cycle {
        @ApiModelProperty("周期编号")
        private Integer cycleIndex;
        @ApiModelProperty("周期开始日")
        private String datePeriodStart;
        @ApiModelProperty("周期结束日(只有这里的周期结束日不是前闭后开规则)")
        private String dateCycleEnd;
    }
}



