package com.mira.user.controller.vo.doctor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("Doctor Hormone")
public class GeneaHormoneVO {
    @ApiModelProperty("hormone 测试结果数据时间戳")
    private String testTime;

    @ApiModelProperty("LH测量值")
    private Float LH;

    @ApiModelProperty("E3G测量值")
    private Float E3G;

    @ApiModelProperty("hCG测量值")
    private Float HCG;

    @ApiModelProperty("PdG测量值")
    private Float PDG;

    @ApiModelProperty("Warning/Error值")
    private String Ecode;
}
