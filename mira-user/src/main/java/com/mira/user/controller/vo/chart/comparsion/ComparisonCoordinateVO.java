package com.mira.user.controller.vo.chart.comparsion;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("阴影渲染")
public class ComparisonCoordinateVO {
    @ApiModelProperty("index")
    private Long index;

    @ApiModelProperty("统计数值")
    private Double metricValue;

    public ComparisonCoordinateVO(Long index, Double metricValue) {
        this.index = index;
        this.metricValue = metricValue;
    }
}
