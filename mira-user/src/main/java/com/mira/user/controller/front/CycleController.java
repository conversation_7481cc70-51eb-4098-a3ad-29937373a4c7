package com.mira.user.controller.front;

import com.mira.user.controller.vo.cycle.CycleAnalysisVO;
import com.mira.user.service.front.ICycleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 周期相关控制器
 *
 * <AUTHOR>
 */
@Api(tags = "13.周期相关")
@RestController
@RequestMapping("/app/v4")
public class CycleController {
    @Resource
    private ICycleService cycleService;

    @ApiOperation("周期分析")
    @GetMapping("/cycle-analysis/view")
    public CycleAnalysisVO cycleAnalysis() {
        return cycleService.cycleAnalysis();
    }
}
