package com.mira.user.controller.vo.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("用户设置的通知相关信息")
public class UserReminderInfoVO {
    @ApiModelProperty("通知标示:0 no remind;1remind")
    private Integer remindFlag;

    @ApiModelProperty("隐藏通知栏信息开关，0-关闭，1-打开")
    private Integer hideContentFlag;

    @ApiModelProperty("周期信息通知开关，0-关闭，1-打开")
    private Integer cyclePhaseFlag;

    @ApiModelProperty("购买建议通知，0-关闭，1-打开")
    private Integer purchaseFlag;

    @ApiModelProperty("隐藏购买建议通知开关，0-关闭，1-打开,默认关闭")
    private Integer hidePurchaseFlag;

    @ApiModelProperty("测试日schedule总开关，0-关闭，不执行，1-按照schedule执行(默认1)")
    private Integer testingScheduleFlag;

    @ApiModelProperty("温度测试推送总开关，0-关闭，1-打开(默认0)")
    private Integer bbtTestingFlag;

    @ApiModelProperty("隐藏温度测试推送开关，0-关闭，1-打开")
    private Integer hideBbtFlag;

    @ApiModelProperty("测试日schedule通知时间戳")
    private Long testingScheduleRemindTime;

    @ApiModelProperty("BBT测试日通知时间戳")
    private Long bbtTestingRemindTime;
}
