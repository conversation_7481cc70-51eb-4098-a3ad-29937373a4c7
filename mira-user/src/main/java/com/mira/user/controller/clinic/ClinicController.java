package com.mira.user.controller.clinic;

import com.mira.api.clinic.dto.ClinicDTO;
import com.mira.user.dto.doctor.ClinicInfoDTO;
import com.mira.user.service.clinic.IClinicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 诊所绑定控制器
 *
 * <AUTHOR>
 */
@Api(tags = "40.诊所绑定")
@RestController
@RequestMapping("/sso/clinic")
public class ClinicController {
    @Resource
    private IClinicService clinicService;

    @Deprecated
    @ApiOperation("绑定诊所")
    @PostMapping("/bind")
    public void bind() {
        clinicService.bind();
    }

    @ApiOperation("1. 绑定诊所 v2")
    @PostMapping("/bind/v2")
    public void bindV2(@RequestParam("clinicCode") String clinicCode) {
        clinicService.bindV2(clinicCode);
    }

    @Deprecated
    @ApiOperation("解除与诊所的绑定")
    @PostMapping("/unbind")
    public void unbind() {
        clinicService.unbind();
    }

    @ApiOperation("2. 解除与诊所的绑定 v2")
    @PostMapping("/unbind/v2")
    public void unbindV2(@RequestParam("clinicCode") String clinicCode) {
        clinicService.unbindV2(clinicCode);
    }

    @ApiOperation("用户拒绝诊所的邀请")
    @PostMapping("/reject")
    public void rejectInvite(@RequestParam("clinicCode") String clinicCode) {
        clinicService.rejectInvite(clinicCode);
    }

    @ApiOperation("3. 获取绑定的诊所列表信息")
    @GetMapping("/bind/clinics")
    public List<ClinicInfoDTO> listBindClinicInfo() {
        return clinicService.listBindClinicInfo();
    }

    @ApiOperation("4. 通过clinicCode获取clinic信息（扫二维码）")
    @GetMapping("/info")
    public ClinicDTO getClinicByCode(@RequestParam("clinicCode") String clinicCode) {
        return clinicService.getClinicByCode(clinicCode);
    }

    @ApiOperation("5. 申请成为诊所的病人")
    @PostMapping("/apply/patient")
    public void applyPatient(@RequestParam("clinicCode") String clinicCode,
                             @RequestParam(value = "doctorId", required = false) Long doctorId) {
        clinicService.applyPatient(clinicCode, doctorId);
    }

}
