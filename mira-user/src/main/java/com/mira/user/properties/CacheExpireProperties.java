package com.mira.user.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * cache expire properties
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Component
@RefreshScope
@ConfigurationProperties(prefix = "cache.expire")
public class CacheExpireProperties {
    /**
     * tips结果
     */
    private long tipsResult;

    /**
     * report
     */
    private long report;

    /**
     * firebase push token
     */
    private long pushToken;
}
