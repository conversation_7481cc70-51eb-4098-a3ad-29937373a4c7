package com.mira.user.service.front.impl;

import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.user.controller.vo.cycle.CycleAnalysisVO;
import com.mira.user.service.front.ICycleService;
import com.mira.user.service.manager.CacheManager;
import com.mira.user.service.manager.CycleManager;
import com.mira.user.service.manager.UserDiaryLogManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 周期相关接口实现
 *
 * <AUTHOR>
 */
@Service
public class CycleServiceImpl implements ICycleService {
    @Resource
    private UserDiaryLogManager userDiaryLogManager;
    @Resource
    private CacheManager cacheManager;
    @Resource
    private CycleManager cycleManager;

    @Resource
    private ISsoProvider ssoProvider;

    @Override
    public CycleAnalysisVO cycleAnalysis() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        AlgorithmResultDTO algorithmResultDTO = cacheManager.getCacheAlgorithmResult(loginUserInfoDTO.getUserId());

        List<Long> sexDateList = userDiaryLogManager.getSexDateTimestamp(loginUserInfoDTO.getUserId());

        return cycleManager.buildCycleAnalysisVO(loginUserInfoDTO, algorithmResultDTO, sexDateList);
    }
}
