package com.mira.user.service.util;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.cycle.TestDataDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.LocalDateUtil;
import com.mira.user.controller.vo.cycle.CycleDataVO;
import com.mira.user.enums.chart.ComparisonEnum;
import com.mira.user.handler.chart.IChartHormoneHandler;
import com.mira.user.handler.chart.hormone.ChartHormoneHandler;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 周期信息工具
 *
 * <AUTHOR>
 */
@SuppressWarnings("all")
public class ComparisonDataUtil {
    /**
     * 取当前周期的hormone测试数据，从当天往前取7天
     *
     * @param wandType     试剂类型
     * @param groupCode    Group Code
     * @param currentCycle 当前周期
     * @param hormoneDTOS  所有hormone测试数据
     * @return List<HormoneDTO>
     */
    public static List<TestDataDTO> comparisonHormoneByCurrentCycle(String wandType,
                                                                    int groupCode,
                                                                    CycleDataDTO currentCycle,
                                                                    List<HormoneDTO> hormoneDTOS) {
        int days = 7;
        List<TestDataDTO> result = new ArrayList<>();
        ComparisonEnum comparisonEnum = ComparisonEnum.get(groupCode, wandType);
        if (comparisonEnum == null) {
            return result;
        }

        String periodStartDate = currentCycle.getDate_period_start() + " 00:00:00";
        CycleDataVO cycleDataVO = new CycleDataVO();
        int chooseCount = 0;
        for (int i = hormoneDTOS.size() - 1; i >= 0; i--) {
            if (chooseCount >= days) {
                break;
            }
            HormoneDTO hormoneDTO = hormoneDTOS.get(i);
            HormoneDTO.TestResult testResult = hormoneDTO.getTest_results();
            if (LocalDateUtil.after(hormoneDTO.getTest_time(), periodStartDate, DatePatternConst.DATE_TIME_PATTERN)) {
                IChartHormoneHandler chartHormoneHandler = ChartHormoneHandler.get(testResult.getWand_type());
                if (Objects.nonNull(chartHormoneHandler)) {
                    chartHormoneHandler.handle(hormoneDTO, cycleDataVO, null);
                    chooseCount++;
                }
            }
        }

        switch (comparisonEnum) {
            case HEALTHY_USERS_LH:
                result = cycleDataVO.getLhDatas().stream().map(vo -> (TestDataDTO) vo).collect(Collectors.toList());
                break;
            case HEALTHY_USERS_E3G:
                result = cycleDataVO.getE3gDatas().stream().map(vo -> (TestDataDTO) vo).collect(Collectors.toList());
                break;
            case HEALTHY_USERS_PDG:
                result = cycleDataVO.getPdgDatas().stream().map(vo -> (TestDataDTO) vo).collect(Collectors.toList());
        }

        // asc
        result.sort(Comparator.comparing(TestDataDTO::getTestTime));

        return result;
    }

    /**
     * 取当前周期的hormone测试数据，从当天往前取7天
     *
     * @param currentCycle 当前周期
     * @param hormoneDTOS  所有hormone测试数据
     * @return List<HormoneDTO>
     */
    public static CycleDataVO hormoneByCurrentCycle(CycleDataDTO currentCycle, List<HormoneDTO> hormoneDTOS) {
        int days = 7;
        String periodStartDate = currentCycle.getDate_period_start() + " 00:00:00";
        CycleDataVO cycleDataVO = new CycleDataVO();
        int chooseCount = 0;
        for (int i = hormoneDTOS.size() - 1; i >= 0; i--) {
            if (chooseCount >= days) {
                break;
            }
            HormoneDTO hormoneDTO = hormoneDTOS.get(i);
            HormoneDTO.TestResult testResult = hormoneDTO.getTest_results();
            if (LocalDateUtil.after(hormoneDTO.getTest_time(), periodStartDate, DatePatternConst.DATE_TIME_PATTERN)) {
                IChartHormoneHandler chartHormoneHandler = ChartHormoneHandler.get(testResult.getWand_type());
                if (Objects.nonNull(chartHormoneHandler)) {
                    chartHormoneHandler.handle(hormoneDTO, cycleDataVO, null);
                    chooseCount++;
                }
            }
        }

        return cycleDataVO;
    }
}
