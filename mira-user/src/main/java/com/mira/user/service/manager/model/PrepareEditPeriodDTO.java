package com.mira.user.service.manager.model;

import com.mira.api.bluetooth.dto.period.UserPeriodParamDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("编辑经期参数")
public class PrepareEditPeriodDTO {
    @ApiModelProperty("时区")
    private String timeZone;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("Last Cycle Flag")
    private Integer lastCycleFlag;

    @ApiModelProperty("用户经期信息参数")
    private UserPeriodParamDTO userPeriodParamDTO;

    @ApiModelProperty("用户经期信息")
    private String periods;

    @ApiModelProperty("是否无经期")
    private Boolean noPeriod = Boolean.FALSE;
}
