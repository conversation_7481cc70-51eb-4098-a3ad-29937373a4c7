package com.mira.user.service.survey.condition;

import com.mira.api.mongo.dto.SurveyCondition;
import com.mira.core.util.LocalDateUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * Churn
 * <br/>
 * 用户流失天数，1:30, 2:60, 3:90
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-04-16
 **/
public class ChurnCondition {
    private final static int NOT_IN_APP_30 = 1;
    private final static int NOT_IN_APP_60 = 2;
    private final static int NOT_IN_APP_90 = 3;

    public static boolean checkChurn(String ipModifyTimeStr, String today, SurveyCondition surveyCondition) {
        Integer churn = surveyCondition.getChurn();
        if (churn == null || churn == -1) {
            return true;
        }
        if (StringUtils.isBlank(ipModifyTimeStr)) {
            return true;
        }

        int minusToDay = LocalDateUtil.minusToDay(today, ipModifyTimeStr);
        if (NOT_IN_APP_30 == churn && minusToDay <= 30) {
            return false;
        }
        if (NOT_IN_APP_60 == churn && minusToDay <= 60) {
            return false;
        }
        if (NOT_IN_APP_90 == churn && minusToDay <= 90) {
            return false;
        }

        return true;
    }
}
