package com.mira.job.schedule.klaviyo.client;

import com.google.gson.Gson;
import com.mira.api.thirdparty.dto.klaviyo.KlaviyoMetricResultDTO;
import com.mira.api.thirdparty.dto.klaviyo.KlaviyoProfileDTO;
import com.mira.api.user.dto.user.AppUserDTO;
import com.mira.core.util.JsonUtil;
import com.mira.job.consts.dto.KlaviyoListDTO;
import com.mira.job.schedule.klaviyo.enums.KlaviyoApiKeyEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * klaviyo api
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class KlaviyoAPI extends AbstractKlaviyoAPI {
    @Resource
    private KlaviyoClient klaviyoClient;

    /**
     * Get Profile ID
     *
     * @param apiKey api key
     * @return Person ID
     */
    public String getProfileID(AppUserDTO appUserDTO, String revision, KlaviyoApiKeyEnum apiKey) {
        String filter = "equals(email,\"" + appUserDTO.getEmail() + "\")";

        KlaviyoListDTO klaviyoListDTO = klaviyoClient.getProfile("Klaviyo-API-Key " + apiKey.getValue(), revision, filter);
        if (CollectionUtils.isEmpty(klaviyoListDTO.getData())) {
            return null;
        }

        return klaviyoListDTO.getData().get(0).getId();
    }

    /**
     * Create Profile
     *
     * @param appUserDTO 用户信息
     * @param revision   接口版本号
     * @param paramMap   请求参数
     */
    public KlaviyoProfileDTO createProfile(AppUserDTO appUserDTO, String revision, Map<String, Object> paramMap) {
        KlaviyoApiKeyEnum apiKey = getApiKey();
        try {
            String profileID = getProfileID(appUserDTO, revision, apiKey);
            if (profileID == null) {
                return klaviyoClient.createProfile("Klaviyo-API-Key " + apiKey.getValue(), revision, paramMap);
            }
        } catch (Exception e) {
            log.error("create profile error", e);
        }
        return null;
    }

    /**
     * Update Profile
     *
     * @param appUserDTO 用户信息
     * @param revision   接口版本号
     * @param params     请求参数
     */
    public Object updateProfile(AppUserDTO appUserDTO, String revision, String params) {
        KlaviyoApiKeyEnum apiKey = getApiKey();
        try {
            String profileID = getProfileID(appUserDTO, revision, apiKey);
            if (profileID == null) {
                return null;
            }

            // 转成bean，并去掉值为null的字段
            KlaviyoProfileDTO klaviyoProfileDTO = new Gson().fromJson(params, KlaviyoProfileDTO.class);
            klaviyoProfileDTO.getData().setId(profileID);
            String newJson = new Gson().toJson(klaviyoProfileDTO);
            // convert map
            Map<String, Object> paramsMap = new Gson().fromJson(newJson, Map.class);
            return klaviyoClient.updateProfile("Klaviyo-API-Key " + apiKey.getValue(), revision,
                    profileID, paramsMap);
        } catch (Exception e) {
            log.error("update profile error", e);
        }
        return null;
    }

    /**
     * Batch Update Profile
     *
     * @param receiveMapList 参数信息
     * @param revision       接口版本号
     */
    public void updateProfileBatch(String revision, List<Map<String, Object>> receiveMapList) {
        for (Map<String, Object> receiveMap : receiveMapList) {
            AppUserDTO appUserDTO = JsonUtil.toObject((String) receiveMap.get("user"), AppUserDTO.class);
            updateProfile(appUserDTO, revision, (String) receiveMap.get("params"));
        }
    }

    /**
     * Get Metric Event
     */
    public KlaviyoMetricResultDTO getProfileEvents(AppUserDTO appUserDTO, String revision) {
        KlaviyoApiKeyEnum apiKey = getApiKey();
        try {
            String profileId = getProfileID(appUserDTO, revision, apiKey);
            if (profileId == null) {
                return null;
            }
            String filter = "equals(profile_id,\"" + profileId + "\")";
            return klaviyoClient.getEvents("Klaviyo-API-Key " + apiKey.getValue(), revision, filter);
        } catch (Exception e) {
            log.error("get metric event error", e);
        }
        return null;
    }
}
