package com.mira.job.schedule.testing.push;

import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.message.dto.FirebasePushDTO;
import com.mira.api.message.enums.NotificationDefineEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.job.consts.dto.JobNotificationDTO;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.consts.enums.ReminderEnum;
import com.mira.job.dal.dao.master.AppUserAlgorithmResultDAO;
import com.mira.job.dal.dao.master.AppUserReminderDAO;
import com.mira.job.dal.dao.master.SysNotificationTestingHistoryDAO;
import com.mira.job.dal.entity.master.*;
import com.mira.job.service.manager.CommonManager;
import com.mira.job.service.manager.JobManager;
import com.mira.job.service.util.FirebaseBuildUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * Test env：0 5,15,25,35,45,55 * * * ?
 * Product env：0 6,16,26,36,46,56 * * * ?
 * BBT 测试日推送
 * 一个用户如果BBT推送开关打开，那么在她指定的推送时间，每天推送一次
 * </p>
 * 发送firebase，并记录sys_notification_record和sys_notification_testing_history
 *
 * @author: xizhao.dai
 * @since: 2023-03-07
 **/
@SuppressWarnings("all")
@Slf4j
@Component
public class PushBbtTestingSchedule extends AbstractPushTesting {
    @Resource
    private AppUserReminderDAO appUserReminderDAO;
    @Resource
    private SysNotificationTestingHistoryDAO sysNotificationTestingHistoryDAO;
    @Resource
    private AppUserAlgorithmResultDAO appUserAlgorithmResultDAO;

    @Resource
    private JobManager jobManager;
    @Resource
    private CommonManager commonManager;

    private final static int ACTIVE_DAY = 30;


    @XxlJob("pushBbtTestingHandler")
    public void testingDayPushHandler() {
        log.info("pushBbtTestingHandler: start execute");
        run();
        log.info("pushBbtTestingHandler: end execute");
        log.info("------------------------------------");
    }

    public void run() {
        Long currentTime = System.currentTimeMillis();
        // 30分钟前
        Long pushBeforeTime = currentTime - 30 * 60 * 1000;
        // 30分钟后
        Long pushAfterTime = currentTime + 30 * 60 * 1000;
        List<AppUserReminderEntity> appUserReminderEntities = appUserReminderDAO.listBbtFlagOpen();
        log.info("BBT flag open size:{}", appUserReminderEntities.size());

        // Get userAlgorithmResults (The modifyTime changed’s userAlgorithmResults )
        List<AppUserAlgorithmResultEntity> algorithmResultEntities = appUserAlgorithmResultDAO.listByGeUpdateTime(pushBeforeTime);
        if (algorithmResultEntities.isEmpty()) {
            return;
        }
        Map<Long, AppUserAlgorithmResultEntity> algorithmResultEntityMap = algorithmResultEntities.stream()
                .collect(Collectors.toMap(AppUserAlgorithmResultEntity::getUserId, Function.identity()));

        List<JobNotificationDTO> jobNotificationDTOS = new ArrayList<JobNotificationDTO>();
        for (AppUserReminderEntity appUserReminderEntity : appUserReminderEntities) {
            String bbtTestingRemindTimeStr = appUserReminderEntity.getBbtTestingRemindTimeStr();
            String timeZone = appUserReminderEntity.getTimeZone();
            String today = ZoneDateUtil.format(timeZone, currentTime, DatePatternConst.DATE_PATTERN);
            if (StringUtils.isBlank(bbtTestingRemindTimeStr)) {
                continue;
            }

            // [Rule: has data in 30 days（active user)]
            AppUserAlgorithmResultEntity algorithmResultEntity = algorithmResultEntityMap.get(appUserReminderEntity.getUserId());
            if (algorithmResultEntity == null) {
                continue;
            }
            List<HormoneDTO> hormoneDataList = JsonUtil.toArray(algorithmResultEntity.getHormoneData(), HormoneDTO.class);
            // 匹配测试时间
            String beforeDays = LocalDateUtil.plusDay(today, -ACTIVE_DAY, DatePatternConst.DATE_PATTERN);
            boolean activeUser = hormoneDataList.stream()
                    .anyMatch(hormoneDTO -> LocalDateUtil.minusToDay(hormoneDTO.getTest_time(), beforeDays) >= 0);
            if (!activeUser) {
                continue;
            }

            Long todayRemindTime = ZoneDateUtil.timestamp(timeZone, today + " " + bbtTestingRemindTimeStr, DatePatternConst.DATE_TIME_PATTERN);
            JobNotificationDTO jobNotificationDTO = getPushNotificationDTO(pushBeforeTime, pushAfterTime,
                    todayRemindTime, NotificationDefineEnum.REMINDER_BBT.getDefineId(), appUserReminderEntity.getUserId(), timeZone);
            if (jobNotificationDTO != null) {
                jobNotificationDTOS.add(jobNotificationDTO);
            }
        }
        push(jobNotificationDTOS, jobManager.getAllUserInfo());
    }

    private void push(List<JobNotificationDTO> jobNotificationDTOS, Map<Long, AppUserInfoEntity> allUserInfoMap) {
        List<SysNotificationTestingHistoryEntity> testingHistoryEntityList = new ArrayList<>();
        Map<FirebasePushDTO, List<Long>> firebasePushMap = new HashMap<>();

        for (JobNotificationDTO jobNotificationDTO : jobNotificationDTOS) {
            // app user info
            Long userId = jobNotificationDTO.getUserId();
            AppUserInfoEntity appUserInfo = allUserInfoMap.get(userId);
            if (appUserInfo == null) {
                continue;
            }
            // notification define
            SysNotificationDefineEntity notificationDefine = commonManager.getNotificationDefine(jobNotificationDTO.getDefineId());
            if (notificationDefine == null) {
                continue;
            }
            // firbase push dto
            FirebasePushDTO firebasePushDTO = FirebaseBuildUtil
                    .buildPushNotification(notificationDefine, jobManager.userRemindOpen(userId, ReminderEnum.HIDE_CONTENT_SWITCH));
            // remind check
            Boolean sendFlag = false;
            boolean remindFlag = jobManager.checkRemindFlag(userId);
            if (remindFlag) {
                firebasePushMap.putIfAbsent(firebasePushDTO, new ArrayList<>());
                firebasePushMap.get(firebasePushDTO).add(userId);
            }
            // sys_notification_testing_history
            Integer pushType = commonManager.getPushType(appUserInfo.getPlatform());
            testingHistoryEntityList.add(buildTestingHistory(jobNotificationDTO, pushType));
        }

        // save testing push history
        if (CollectionUtils.isNotEmpty(testingHistoryEntityList)) {
            sysNotificationTestingHistoryDAO.saveBatch(testingHistoryEntityList);
        }

        // push
        log.info("-----------------------------------------");
        long[] pushCount = {0L};
        firebasePushMap.values().forEach(list -> pushCount[0] += list.size());
        log.info("PushBbtTestingSchedule -> Start firebase push, type size:{}, push count:{}", firebasePushMap.size(), pushCount[0]);
        commonManager.firebasePush(firebasePushMap, buildPushUserInfoDTO(allUserInfoMap), null);
    }

    private JobNotificationDTO getPushNotificationDTO(Long pushBeforeTime, Long pushAfterTime,
                                                      Long firstReminderTime, Long defineId,
                                                      Long userId, String timeZone) {
        if (firstReminderTime != null && firstReminderTime > pushBeforeTime && firstReminderTime < pushAfterTime) {
            boolean exist = sysNotificationTestingHistoryDAO.checkExist(userId, defineId, firstReminderTime);
            if (!exist) {
                return new JobNotificationDTO(userId, timeZone, defineId, firstReminderTime);
            }
        }
        return null;
    }

    private Map<Long, PushUserInfoDTO> buildPushUserInfoDTO(Map<Long, AppUserInfoEntity> allUserInfoMap) {
        Map<Long, PushUserInfoDTO> pushUserInfoDTOMap = new HashMap<>();
        for (Map.Entry<Long, AppUserInfoEntity> entry : allUserInfoMap.entrySet()) {
            Long userId = entry.getKey();
            AppUserInfoEntity value = entry.getValue();

            PushUserInfoDTO pushUserInfoDTO = new PushUserInfoDTO();
            pushUserInfoDTO.setUserId(userId);
            pushUserInfoDTO.setPushToken(value.getPushToken());
            pushUserInfoDTO.setPlatform(value.getPlatform());
            pushUserInfoDTO.setTimeZone(value.getTimeZone());
            pushUserInfoDTOMap.put(userId, pushUserInfoDTO);
        }
        return pushUserInfoDTOMap;
    }
}
