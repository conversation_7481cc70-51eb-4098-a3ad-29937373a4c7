package com.mira.job.schedule.rateapp;

import com.google.common.collect.Lists;
import com.mira.api.message.enums.NotificationDefineEnum;
import com.mira.core.consts.enums.LocalEnum;
import com.mira.core.util.StringListUtil;
import com.mira.job.dal.dao.master.SysNotificationDefineDAO;
import com.mira.job.dal.dao.master.SysNotificationRecordDAO;
import com.mira.job.dal.dao.master.SysRateAppLogDAO;
import com.mira.job.dal.entity.master.AppUserEntity;
import com.mira.job.dal.entity.master.SysNotificationDefineEntity;
import com.mira.job.dal.entity.master.SysNotificationRecordEntity;
import com.mira.job.dal.entity.master.SysRateAppLogEntity;
import com.mira.job.schedule.AbstractSchedule;
import com.mira.job.schedule.rateapp.build.AmazonRateBuild;
import com.mira.job.schedule.rateapp.build.GeneralRateBuild;
import com.mira.job.service.manager.JobManager;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 好评邀请
 * <p>
 * 只增加notification record记录，不发送farebase
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2022-03-09
 **/
@Slf4j
@Component
public class RateTheAppSchedule extends AbstractSchedule {
    @Resource
    private SysNotificationRecordDAO sysNotificationRecordDAO;
    @Resource
    private SysNotificationDefineDAO sysNotificationDefineDAO;
    @Resource
    private SysRateAppLogDAO sysRateAppLogDAO;

    @Resource
    private JobManager jobManager;
    @Resource
    private GeneralRateBuild generalRateBuild;
    @Resource
    private AmazonRateBuild amazonRateBuild;

    @XxlJob("rateTheAppHandler")
    public void rateTheAppHandler() {
        log.info("rateTheAppHandler: start execute");
        run();
        log.info("rateTheAppHandler: end execute");
        log.info("------------------------------------");
    }

    /**
     * Conditions: User did not rate the app yet And User did not decline rating the app (system)
     * <p>
     * User type:
     * defineId=26: Active users
     * defineId=27: Users who canceled more than 30 days ago (still active)
     * defineId=28: Users who gave us 1-3 stars more than 30 days ago (still active)
     * <p>
     * Trigger:
     * 26: who have more than 1 real cycle data. and recent 2 real cycle has more than 3 testing data
     * 27: 30 days since previous rate the app notification cancelled
     * 28: 60 days since previous rate the app notification rated 1-3
     */
    private void run() {
        // 获取所有用户
        Map<Long, AppUserEntity> allUser = jobManager.getAllUser();

        // ------------------------- 构建所有用户 -------------------------

        // 还没有评分过的用户
        List<Long> generalUserNoCommentsList = generalRateBuild.buildNoCommentsUserList(allUser);

        // 收到弹窗但没有评分的用户，30天后再次弹窗
        List<SysRateAppLogEntity> generalUserRateAppLogList = sysRateAppLogDAO.list();
        List<Long> generalUserAfter30DaysList = generalRateBuild.build30DaysUserList(generalUserRateAppLogList);

        // 评分1~3星的用户，60天后再次弹窗
        List<Long> generalUserAfter60DaysList = generalRateBuild.build60DaysUserList(generalUserRateAppLogList);

        // ------------------------- 构建亚马逊用户 -------------------------

        List<Long> amazonUserNoCommentsList = amazonRateBuild.buildNoCommentsUserList(allUser);

        List<SysRateAppLogEntity> amazonUserRateAppLogList = sysRateAppLogDAO.getListByAmazon();
        List<Long> amazonUserAfter30DaysList = amazonRateBuild.build30DaysUserList(amazonUserRateAppLogList);

        // ------------------------- 发送弹窗，新增记录 -------------------------

        generalUserNoCommentsList.removeAll(amazonUserNoCommentsList);
        generalUserAfter30DaysList.removeAll(amazonUserAfter30DaysList);

        // 发送弹窗
        sendPopUp(generalUserNoCommentsList, generalUserAfter30DaysList, generalUserAfter60DaysList);
        sendPopUp(amazonUserNoCommentsList, amazonUserAfter30DaysList, null);

        // 新增评分记录
        addRateAppLog(generalUserNoCommentsList, 0, "Asia/Shanghai");
        addRateAppLog(amazonUserNoCommentsList, 0, "Asia/Shanghai");
    }

    private void sendPopUp(List<Long> noCommentsUserList,
                           List<Long> after30DaysUserList,
                           List<Long> after60DaysUserList) {
        SysNotificationDefineEntity activeUsersEntity =
                sysNotificationDefineDAO.getByDefineId(NotificationDefineEnum.RATE_THE_APP_ACTIVE_USERS.getDefineId(), LocalEnum.LOCAL_US.getValue());
        SysNotificationDefineEntity cancelledUsersEntity =
                sysNotificationDefineDAO.getByDefineId(NotificationDefineEnum.RATE_THE_APP_CANCELLED.getDefineId(), LocalEnum.LOCAL_US.getValue());
        SysNotificationDefineEntity lowStarUsersEntity =
                sysNotificationDefineDAO.getByDefineId(NotificationDefineEnum.RATE_THE_APP_LOW_STAR.getDefineId(), LocalEnum.LOCAL_US.getValue());

        if (CollectionUtils.isNotEmpty(noCommentsUserList)) {
            batchSaveNotificationRecord(noCommentsUserList, activeUsersEntity,
                    3, "Asia/Shanghai", 0, 0);
        }
        if (CollectionUtils.isNotEmpty(after30DaysUserList)) {
            batchSaveNotificationRecord(after30DaysUserList, cancelledUsersEntity,
                    3, "Asia/Shanghai", 0, 0);
        }
        if (CollectionUtils.isNotEmpty(after60DaysUserList)) {
            batchSaveNotificationRecord(after60DaysUserList, lowStarUsersEntity,
                    3, "Asia/Shanghai", 0, 0);
        }
    }

    /**
     * 添加用户好评邀请评价记录
     *
     * @param userIds  userIds
     * @param score    vote(1,2,3,4,5),-1,-2 means cancelled to vote, 0 means add a log
     * @param timeZone user local timezone
     */
    private void addRateAppLog(List<Long> userIds, Integer score, String timeZone) {
        if (userIds.isEmpty()) {
            return;
        }
        List<SysRateAppLogEntity> rateAppLogEntities = new ArrayList<>();
        List<List<Long>> partition = Lists.partition(userIds, 500);
        for (List<Long> ids : partition) {
            rateAppLogEntities.addAll(sysRateAppLogDAO.listByUserIds(ids));
        }

        List<SysRateAppLogEntity> batchSaveList = new ArrayList<>();
        List<SysRateAppLogEntity> batchUpdateList = new ArrayList<>();
        if (rateAppLogEntities.isEmpty()) {
            for (Long userId : userIds) {
                SysRateAppLogEntity rateAppLogEntity = new SysRateAppLogEntity();
                rateAppLogEntity.setUserId(userId);
                rateAppLogEntity.setScore(score);
                rateAppLogEntity.setScoreHis(StringListUtil.listToString(Arrays.asList(score), ","));
                UpdateEntityTimeUtil.setBaseEntityTime(timeZone, rateAppLogEntity);
                batchSaveList.add(rateAppLogEntity);
            }
        } else {
            for (Long userId : userIds) {
                SysRateAppLogEntity rateLogEntity = rateAppLogEntities.stream()
                        .filter(sysRateAppLogEntity -> sysRateAppLogEntity.getUserId().equals(userId))
                        .findFirst()
                        .orElse(null);
                if (rateLogEntity == null) {
                    rateLogEntity = new SysRateAppLogEntity();
                    rateLogEntity.setUserId(userId);
                    rateLogEntity.setScore(score);
                    rateLogEntity.setScoreHis(StringListUtil.listToString(Arrays.asList(score), ","));
                    UpdateEntityTimeUtil.setBaseEntityTime(timeZone, rateLogEntity);
                    batchSaveList.add(rateLogEntity);
                } else {
                    rateLogEntity.setScore(score);
                    List<Integer> scoreHisList = StringListUtil.strToIntegerList(rateLogEntity.getScoreHis(), ",");
                    List<Integer> newScoreHis = new ArrayList<>(scoreHisList);
                    if (newScoreHis.size() >= 50) {
                        newScoreHis.remove(0);
                    }
                    newScoreHis.add(score);
                    rateLogEntity.setScoreHis(StringListUtil.listToString(newScoreHis, ","));
                    UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, rateLogEntity);
                    batchUpdateList.add(rateLogEntity);
                }
            }
        }

        if (!batchSaveList.isEmpty()) {
            sysRateAppLogDAO.saveBatch(batchSaveList);
        }
        if (!batchUpdateList.isEmpty()) {
            sysRateAppLogDAO.updateBatchById(batchUpdateList);
        }
    }

    /**
     * 批量保存通知信息
     *
     * @param userIds              用户id列表
     * @param notificationDefine   通知定义记录
     * @param pushType             推送类型：0客户端本地推送；1firebase iOS推送;2firebase 安卓推送
     * @param pushStatus           推送状态：0成功；-1未成功
     * @param timeZone             时区
     */
    private void batchSaveNotificationRecord(List<Long> userIds, SysNotificationDefineEntity notificationDefine, Integer pushType,
                                            String timeZone, Integer read, Integer pushStatus) {

        List<SysNotificationRecordEntity> sysNotificationRecordEntities = new ArrayList<>();
        for (Long userId : userIds) {
            if (checkPfizerAccount(userId)) {
                continue;
            }
            SysNotificationRecordEntity sysNotificationRecordEntity = new SysNotificationRecordEntity();
            sysNotificationRecordEntity.setUserId(userId);
            sysNotificationRecordEntity.setNotificationDefineId(notificationDefine.getDefineId());
            sysNotificationRecordEntity.setPushType(pushType);
            sysNotificationRecordEntity.setPushStatus(pushStatus);
            sysNotificationRecordEntity.setRead(read);
            sysNotificationRecordEntity.setContent(notificationDefine.getContent());
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, sysNotificationRecordEntity);
            sysNotificationRecordEntities.add(sysNotificationRecordEntity);
        }
        if (!sysNotificationRecordEntities.isEmpty()) {
            sysNotificationRecordDAO.saveBatch(sysNotificationRecordEntities);
        }
    }
}
