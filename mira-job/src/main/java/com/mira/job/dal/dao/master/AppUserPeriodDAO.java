package com.mira.job.dal.dao.master;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.job.dal.DataSourceName;
import com.mira.job.dal.entity.master.AppUserPeriodEntity;
import com.mira.job.dal.mapper.master.AppUserPeriodMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * app_user_period DAO
 *
 * <AUTHOR>
 */
@DS(DataSourceName.SLAVE)
@Repository
public class AppUserPeriodDAO extends ServiceImpl<AppUserPeriodMapper, AppUserPeriodEntity> {
    public AppUserPeriodEntity getByUserId(Long userId) {
        return getOne(Wrappers.<AppUserPeriodEntity>lambdaQuery()
                              .eq(AppUserPeriodEntity::getUserId, userId));
    }

    public List<AppUserPeriodEntity> listByUserIds(List<Long> userIds) {
        return list(Wrappers.<AppUserPeriodEntity>lambdaQuery()
                            .in(AppUserPeriodEntity::getUserId, userIds));
    }


}
