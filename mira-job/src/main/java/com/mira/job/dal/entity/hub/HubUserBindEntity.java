package com.mira.job.dal.entity.hub;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户是否绑定仪器
 * <p>
 * from app_user_bind
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("hub_user_bind")
public class HubUserBindEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String hubId;

    /**
     * 绑定时间
     */
    private String bindTime;

    /**
     * 设备版本
     */
    private String bindVersion;

    /**
     * sn
     */
    private String sn;
    /**
     * 修改时间
     */
    private Long modifyTime;


    /**
     * 同步时间
     */
    private Long syncTime;

}
