package com.mira.job.service.handler.desk.notification.condition;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.job.dto.NotificationPushCreateDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.service.handler.desk.notification.INotificationJobHandler;
import com.mira.job.service.handler.desk.notification.NotificationJobHandler;
import com.mira.api.bluetooth.util.CycleDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * No of tests taken in this cycle
 * <br/>
 * 周期内测试次数，1,2,3,4,5...33,34,35,35+
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TestsCountHandler implements INotificationJobHandler<NotificationPushCreateDTO, List<PushUserInfoDTO>> {
    @PostConstruct
    public void init() {
        NotificationJobHandler.add(this);
    }

    @Override
    public void handle(NotificationPushCreateDTO dto, List<PushUserInfoDTO> allUserInfo) {
        Integer[] testsCountCondition = dto.getTestsCount();
        if (testsCountCondition == null || testsCountCondition.length == 0) {
            return;
        }
        List<Integer> testsCountConditionList = List.of(testsCountCondition);

        List<PushUserInfoDTO> waitDeleteList = new ArrayList<>();

        for (PushUserInfoDTO userInfo : allUserInfo) {
            if (StringUtils.isBlank(userInfo.getCycleData())) {
                waitDeleteList.add(userInfo);
                continue;
            }
            String today = ZoneDateUtil.format(userInfo.getTimeZone(), System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
            try {
                // cycle
                List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(userInfo.getCycleData(), CycleDataDTO.class);
                CycleDataDTO currentCycleData = CycleDataUtil.getCurrentCycleData(today, cycleDataDTOS);
                // hormone
                List<HormoneDTO> hormoneDTOS = JsonUtil.toArray(userInfo.getHormoneData(), HormoneDTO.class);
                List<HormoneDTO> currentCycleHormones = CycleDataUtil.hormoneByCurrentCycle(currentCycleData, hormoneDTOS);

                long testsCount = currentCycleHormones.stream()
                        .filter(hormone -> hormone.getFlag() == 1)
                        .count();
                // 单选35+
                if (testsCountConditionList.size() == 1 && testsCountConditionList.get(0) == 36
                        && testsCount < 36) {
                    waitDeleteList.add(userInfo);
                    continue;
                }
                // 包含35+
                if (testsCountConditionList.contains(36)
                        && testsCount > 35)  {
                    continue;
                }
                if (!testsCountConditionList.contains(Long.valueOf(testsCount).intValue())) {
                    waitDeleteList.add(userInfo);
                }
            } catch (Exception e) {
                log.error("[Desk Job] user:{} tests count handler error.", userInfo.getUserId(), e);
            }
        }

        if (CollectionUtils.isNotEmpty(waitDeleteList)) {
            allUserInfo.removeAll(waitDeleteList);
        }
    }
}
