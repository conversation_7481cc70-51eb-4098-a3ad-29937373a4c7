package com.mira.bluetooth.dto.algorithm.response;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ApiModel("长周期")
public class LongerPeriodReturnDTO {
    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("周期数据")
    private List<CycleDataDTO> cycle_data;
}
