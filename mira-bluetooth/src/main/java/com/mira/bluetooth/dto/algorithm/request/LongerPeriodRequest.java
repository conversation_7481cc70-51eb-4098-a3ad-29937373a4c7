package com.mira.bluetooth.dto.algorithm.request;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ApiModel("长周期请求体")
public class LongerPeriodRequest extends BaseRequest {
    @ApiModelProperty("周期数据")
    private List<CycleDataDTO> cycle_data;
}
