package com.mira.bluetooth.async;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.thirdparty.consts.KlaviyoMetricConst;
import com.mira.api.thirdparty.consts.KlaviyoPropertyConst;
import com.mira.api.thirdparty.dto.klaviyo.KlaviyoMetricEventDTO;
import com.mira.api.thirdparty.dto.klaviyo.KlaviyoProfileDTO;
import com.mira.api.thirdparty.provider.IKlaviyoProvider;
import com.mira.api.user.dto.user.AppUserDTO;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.api.user.provider.IUserProvider;
import com.mira.bluetooth.dto.algorithm.request.NewHormoneDataRequest;
import com.mira.bluetooth.dto.algorithm.response.AlgorithmReturnDTO;
import com.mira.core.consts.HeaderConst;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.web.properties.SysDictProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Predicate;

/**
 * klaviyo 消息生产者
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class KlaviyoProducer {
    @Resource
    private SysDictProperties sysDictProperties;

    @Resource
    private IKlaviyoProvider klaviyoProvider;
    @Resource
    private IUserProvider userProvider;

    private final static String IP = "ip";
    private final static String COUNTRYCODE = "countryCode";
    private final static String CONTINENTCODE = "continentCode";
    private final static String TIME_FULL_PATTERN = "yyyy-MM-dd HH:mm:ss";
    private final static String DATE_FULL_PATTERN = "yyyy-MM-dd";
    private final static String TARGET_TIME_PATTERN = "MM/dd/yy";

    /**
     * 上传测试数据进行测试
     */
    public void testRecord(Long userId, NewHormoneDataRequest newHormoneDataRequest, AlgorithmReturnDTO algorithmReturnData) {
        if (!klaviyoSwitchOpen()) {
            return;
        }
        AppUserDTO appUserDTO = userProvider.getUserById(userId).getData();

        String ip = ContextHolder.get(HeaderConst.IP);

        CompletableFuture.supplyAsync(() -> {
            // 最新的周期数据
            CycleDataDTO cycleDataDTO = algorithmReturnData.getCycle_data().get(algorithmReturnData.getCycle_data().size() - 1);

            // 历史检测不为空，或者没有有效测试数据，说明是第一次测试
            Predicate<NewHormoneDataRequest> firstTest = data -> {
                boolean isEmpty = CollectionUtils.isEmpty(data.getHormone_data_history());
                boolean validHistoryTest = data.getHormone_data_history()
                        .stream().anyMatch(hormone -> hormone.getFlag() == 1);
                return isEmpty || (!validHistoryTest);
            };

            // 有效检测次数
            long validCount = 1L;
            long count = newHormoneDataRequest.getHormone_data_history()
                    .stream().filter(hormone -> hormone.getFlag() == 1)
                    .count();
            validCount += count;

            // 当日是否易孕期开始日
            Integer userMode = newHormoneDataRequest.getUser_mode();
            Predicate<AlgorithmReturnDTO> predictedFertile = data -> {
                // 只针对TTC、OFT、Cycle tracking用户
                ArrayList<Integer> userGoalList = Lists.newArrayList(UserGoalEnum.CYCLE_TRACKING.getValue(), UserGoalEnum.TTC.getValue(), UserGoalEnum.OFT.getValue());
                boolean bUserGoal = userGoalList.contains(userMode);
                // 周期状态为实周期或预测周期
                boolean bCycleStatus = CycleStatusEnum.REAL_CYCLE.getStatus() == cycleDataDTO.getCycle_status()
                        || CycleStatusEnum.FORECAST_CYCLE.getStatus() == cycleDataDTO.getCycle_status();
                return bUserGoal && bCycleStatus;
            };

            // 预测周期开始日，算法数据里最新的一个周期是预测周期
            Predicate<AlgorithmReturnDTO> predictedPeriod = data
                    -> CycleStatusEnum.FORECAST_CYCLE.getStatus() == cycleDataDTO.getCycle_status();

            // 构建参数并发送
            Map<String, Object> properties = Maps.newHashMap();
            buildCommonProperty(ip, appUserDTO, properties);
            properties.put(KlaviyoPropertyConst.NUMBER_OF_TESTS_TAKEN, String.valueOf(validCount));
            properties.put(KlaviyoPropertyConst.LAST_TEST_TAKEN,
                    LocalDateUtil.format(newHormoneDataRequest.getHormone_data_new().get(0).getTest_time(), TIME_FULL_PATTERN, TARGET_TIME_PATTERN));

            if (firstTest.test(newHormoneDataRequest)) {
                properties.put(KlaviyoPropertyConst.FIRST_TEST_TAKEN, properties.get(KlaviyoPropertyConst.LAST_TEST_TAKEN));
            }
            if (predictedFertile.test(algorithmReturnData)) {
                properties.put(KlaviyoPropertyConst.PREDICTED_FERTILE_WINDOW_START_DATE,
                        LocalDateUtil.format(cycleDataDTO.getDate_FW_start(), DATE_FULL_PATTERN, TARGET_TIME_PATTERN));
            }
            if (predictedPeriod.test(algorithmReturnData)) {
                properties.put(KlaviyoPropertyConst.PREDICTED_PERIOD_DATE,
                        LocalDateUtil.format(cycleDataDTO.getDate_period_start(), DATE_FULL_PATTERN, TARGET_TIME_PATTERN));
            }

            updateProfileEvent(appUserDTO, properties);
            createMetricEvent(appUserDTO, KlaviyoMetricConst.TEST_TAKEN);

            return "";
        }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
            log.error("func:[testRecord] Error:", ex);
            return ex.getMessage();
        });
    }

    /**
     * menopause event
     */
    public void addMenopauseProperty(Long userId, String klaviyoPropertyKey, String value) {
        if (!klaviyoSwitchOpen()) {
            return;
        }
        String ip = ContextHolder.get(HeaderConst.IP);
        AppUserDTO appUserDTO = userProvider.getUserById(userId).getData();
        CompletableFuture.supplyAsync(() -> {
            Map<String, Object> properties = Maps.newHashMap();
            buildCommonProperty(ip, appUserDTO, properties);
            properties.put(klaviyoPropertyKey, String.valueOf(value));

            updateProfileEvent(appUserDTO, properties);

            return "";
        }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
            log.error("func:[editCycleLength] Error:", ex);
            return ex.getMessage();
        });
    }

    private void buildCommonProperty(String ip, AppUserDTO appUserDTO, Map<String, Object> properties) {
        properties.put(IP, ip);
        properties.put(COUNTRYCODE, appUserDTO.getCountryCode());
        properties.put(CONTINENTCODE, appUserDTO.getContinentCode());
    }

    /**
     * 更新 profile
     */
    private void updateProfileEvent(AppUserDTO appUserDTO, Map<String, Object> properties) {
        KlaviyoProfileDTO.Attributes attributes = new KlaviyoProfileDTO.Attributes();
        attributes.setProperties(properties);

        KlaviyoProfileDTO.Data data = new KlaviyoProfileDTO.Data();
        data.setAttributes(attributes);

        KlaviyoProfileDTO klaviyoProfileDTO = new KlaviyoProfileDTO();
        klaviyoProfileDTO.setData(data);

        Map<String, Object> sendMap = new HashMap<>();
        sendMap.put("flag", "updateProfile");
        sendMap.put("user", JsonUtil.toJson(appUserDTO));
        sendMap.put("params", new Gson().toJson(klaviyoProfileDTO));

        klaviyoProvider.appEvent(sendMap);
    }

    /**
     * 添加 Metric 事件
     */
    private void createMetricEvent(AppUserDTO appUserDTO, String metricName) {
        // 使用 Gson，否则$开头的变量不会转化
        String value = new Gson().toJson(new KlaviyoMetricEventDTO(appUserDTO.getEmail(), metricName, metricName));
        Map<String, Object> metricEventMap = new HashMap<>();
        metricEventMap.put("flag", "metricEvent");
        metricEventMap.put("user", JsonUtil.toJson(appUserDTO));
        metricEventMap.put("params", value);

        klaviyoProvider.appEvent(metricEventMap);
    }

    /**
     * klaviyo 开关
     *
     * @return true/false
     */
    private boolean klaviyoSwitchOpen() {
        return "1".equals(sysDictProperties.getKlaviyoSwitch());
    }
}
