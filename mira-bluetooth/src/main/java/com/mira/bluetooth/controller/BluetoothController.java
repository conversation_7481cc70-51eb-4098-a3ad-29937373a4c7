package com.mira.bluetooth.controller;

import com.mira.api.user.dto.user.UserBindDTO;
import com.mira.api.user.dto.user.UserBindVersionDTO;
import com.mira.bluetooth.controller.vo.DataHistoryVO;
import com.mira.bluetooth.controller.vo.DataUploadVO;
import com.mira.bluetooth.dto.DataUploadDTO;
import com.mira.bluetooth.service.IBluetoothService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 蓝牙数据控制器
 *
 * <AUTHOR>
 */
@Api(tags = "蓝牙数据")
@RestController
@RequestMapping("/bluetooth/v4")
public class BluetoothController {
    @Resource
    private IBluetoothService bluetoothService;

    @ApiOperation("设备上传蓝牙数据")
    @PostMapping("/data/upload")
    public DataUploadVO upload(@Valid @RequestBody DataUploadDTO dataUploadDTO) {
        return bluetoothService.upload(dataUploadDTO);
    }

    @ApiOperation("获取用户最新上传的历史时间")
    @GetMapping("/data/max-complete")
    public Long getUserMaxCompleteTime() {
        return bluetoothService.getUserMaxCompleteTime();
    }

    @Deprecated(since = "v7.6.48")
    @ApiOperation("获取某一天的测试历史")
    @GetMapping("/data/test-history")
    public DataHistoryVO getHistory(@RequestParam String dateStr) {
        return bluetoothService.getHistory(dateStr);
    }

    @ApiOperation("绑定或者解绑仪器")
    @PostMapping("/info/bind")
    public Integer bind(@RequestBody UserBindDTO userBindDTO) {
        return bluetoothService.bind(userBindDTO);
    }

    @ApiOperation("修改绑定的firmware版本")
    @PostMapping("/info/edit/bind-version")
    public void editBindVersion(@RequestBody UserBindVersionDTO userBindVersionDTO) {
        bluetoothService.editBindVersion(userBindVersionDTO);
    }

    @ApiOperation("修改bind-flag字段：wait-shipping")
    @PostMapping("/info/wait-shipping")
    public void waitShipping() {
        bluetoothService.waitShipping();
    }

}
