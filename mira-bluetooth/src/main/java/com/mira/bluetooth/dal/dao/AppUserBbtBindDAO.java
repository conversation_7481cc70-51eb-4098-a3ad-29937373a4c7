package com.mira.bluetooth.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.bluetooth.dal.entity.AppUserBbtBindEntity;
import com.mira.bluetooth.dal.mapper.AppUserBbtBindMapper;
import org.springframework.stereotype.Repository;

@Repository
public class AppUserBbtBindDAO extends ServiceImpl<AppUserBbtBindMapper, AppUserBbtBindEntity> {
    public AppUserBbtBindEntity getByUserId(Long userId) {
        return getOne(Wrappers.<AppUserBbtBindEntity>lambdaQuery()
                .eq(AppUserBbtBindEntity::getUserId, userId));
    }
}
