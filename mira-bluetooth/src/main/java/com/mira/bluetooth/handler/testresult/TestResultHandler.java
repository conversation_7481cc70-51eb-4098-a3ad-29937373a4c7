package com.mira.bluetooth.handler.testresult;

import com.mira.bluetooth.handler.ITestResultHandler;

import java.util.HashMap;
import java.util.Map;

/**
 * Chart页激素数据处理器
 *
 * <AUTHOR>
 */
@SuppressWarnings("all")
public class TestResultHandler {
    private final static Map<String, ITestResultHandler> HANDLER_MAP = new HashMap<>();

    public static void set(String wangType, ITestResultHandler chartHormoneHandler) {
        HANDLER_MAP.put(wangType, chartHormoneHandler);
    }

    public static ITestResultHandler get(String wandType) {
        return HANDLER_MAP.get(wandType);
    }
}
