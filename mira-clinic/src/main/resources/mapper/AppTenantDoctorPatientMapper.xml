<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mira.clinic.dal.mapper.AppTenantDoctorPatientMapper">
    <select id="listTenantDoctorPatientListDTO"
            resultType="com.mira.clinic.dto.TenantDoctorPatientListDTO">
        select tdp.patient_id, tdp.doctor_id, td.name, td.email
        from app_tenant_doctor_patient tdp
        LEFT JOIN app_tenant_doctor td on td.id = tdp.doctor_id
        where tdp.patient_id in
        <foreach item="patientId" index="index" collection="patientIds" open="(" separator="," close=")">
            #{patientId}
        </foreach>
    </select>

    <select id="listDoctorPatientDTO"
            resultType="com.mira.api.clinic.dto.DoctorPatientDTO">
        select p.tenant_code, p.user_id, p.patient_number, tdp.patient_id, tdp.doctor_id
        from app_tenant_patient p
                 INNER JOIN app_tenant_doctor_patient tdp on p.id = tdp.patient_id
        where p.user_id = #{userId}
          and p.deleted = 0
          and tdp.deleted = 0
          and p.status = 2
    </select>

    <delete id="removeDoctorPatient">
        delete from app_tenant_doctor_patient where patient_id = #{patientId}
    </delete>

    <delete id="removeByDoctorId">
        delete from app_tenant_doctor_patient where doctor_id = #{doctorId}
    </delete>

    <delete id="removeInDoctorPatient">
        delete from app_tenant_doctor_patient where patient_id = #{patientId}
        and doctor_id in
        <foreach item="doctorId" index="index" collection="doctorIds" open="(" separator="," close=")">
            #{doctorId}
        </foreach>
    </delete>

</mapper>
