<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mira.clinic.dal.mapper.AppTenantDoctorNoteMapper">
    <select id="listNote"
            resultType="com.mira.clinic.dto.TenantDoctorNoteDTO">
        select tdn.id, tdn.content, tdn.doctor_id, tdn.create_time_str, td.name, td.role
        from app_tenant_doctor_note tdn
                 LEFT JOIN app_tenant_doctor td on td.id = tdn.doctor_id
        where tdn.patient_id = #{patientId}
          and tdn.tenant_code = #{tenantCode}
          and tdn.deleted = 0
        order by tdn.modify_time desc
    </select>

</mapper>
