package com.mira.clinic.controller.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

@Getter
@Setter
@ApiModel("登录请求参数")
public class LoginDTO {
    @ApiModelProperty(value = "邮箱", required = true)
    @NotBlank(message = "Email address or password should not be empty.")
    private String email;

    @ApiModelProperty(value = "密码", required = true)
    @NotBlank(message = "Email address or password should not be empty.")
    private String password;

    @ApiModelProperty(value = "验证码uid", required = true)
    @NotBlank(message = "Request param error.")
    private String uid;

    @ApiModelProperty(value = "验证码", required = true)
    @NotBlank(message = "Request param error.")
    private String verifyCode;
}
