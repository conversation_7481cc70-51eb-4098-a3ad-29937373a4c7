package com.mira.clinic.controller.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("Setting结果")
public class TenantSettingVO {
    @ApiModelProperty("tenant code")
    private String tenantCode;

    @ApiModelProperty("period started notification 1为打开")
    private Integer notificationPeriodStarted;

    @ApiModelProperty("period edited notification 1为打开")
    private Integer notificationPeriodEdited;

    @ApiModelProperty("LH surge notification 1为打开")
    private Integer notificationLhSurge;

    @ApiModelProperty("first test taken 1为打开 默认打开")
    private Integer notificationFirstTest;

    @ApiModelProperty("Positive HCG notification 1为打开 默认打开")
    private Integer notificationPositiveHCG;

    @ApiModelProperty("Pregnant notification 1为打开 默认打开")
    private Integer notificationPregnant;
}
