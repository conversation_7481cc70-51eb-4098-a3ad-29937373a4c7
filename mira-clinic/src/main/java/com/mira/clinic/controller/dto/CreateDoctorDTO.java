package com.mira.clinic.controller.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("创建医生请求参数")
public class CreateDoctorDTO {
    @ApiModelProperty(value = "管理员名称", required = true)
    private String name;

    @ApiModelProperty(value = "管理员email", required = true)
    private String email;

    @ApiModelProperty(value = "管理员角色编码:2:医生;3:护士", required = true)
    private Integer role;
}
