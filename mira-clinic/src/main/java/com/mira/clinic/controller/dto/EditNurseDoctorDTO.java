package com.mira.clinic.controller.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ApiModel("修改护士医生关联关系请求参数")
public class EditNurseDoctorDTO {
    @ApiModelProperty(value = "护士id", required = true)
    private Long nurseId;

    @ApiModelProperty("关联医生列表")
    private List<Long> doctorIds;
}
