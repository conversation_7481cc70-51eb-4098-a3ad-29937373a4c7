package com.mira.clinic.controller;

import com.mira.clinic.controller.dto.CreatePatientNoteDTO;
import com.mira.clinic.controller.vo.TenantDoctorNoteVO;
import com.mira.clinic.service.IDoctorNoteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 医生数据管理
 *
 * <AUTHOR>
 */
@Api(tags = "医生数据管理")
@RestController
@RequestMapping("/tenant/doctor")
public class DoctorNoteController {
    @Resource
    private IDoctorNoteService doctorNoteService;

    @ApiOperation("增加Note")
    @PostMapping("/note/create")
    public void createNote(@RequestBody CreatePatientNoteDTO createPatientNoteDTO) {
        doctorNoteService.createPatientNote(createPatientNoteDTO);
    }

    @ApiOperation("删除Note")
    @PostMapping("/note/delete")
    public void deleteNote(@RequestParam Long noteId) {
        doctorNoteService.deletePatientNote(noteId);
    }

    @ApiOperation("医生关于指定病人的note列表")
    @PostMapping("/note/list")
    public List<TenantDoctorNoteVO> noteList(@RequestParam Long patientId) {
        return doctorNoteService.noteList(patientId);
    }
}
