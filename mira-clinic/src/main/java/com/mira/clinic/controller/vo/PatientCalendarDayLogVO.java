package com.mira.clinic.controller.vo;

import com.mira.api.bluetooth.dto.wand.WandTestBiomarkerDTO;
import com.mira.api.user.dto.user.TemperatureDTO;
import com.mira.api.user.dto.user.diary.UserDiaryMoodsDTO;
import com.mira.api.user.dto.user.diary.UserMedicineDTO;
import com.mira.api.user.dto.user.diary.UserSymptomDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@ApiModel("日历页每日log数据")
public class PatientCalendarDayLogVO {
    @ApiModelProperty("时间，e.g: 2022-04-22")
    private String date;

    @ApiModelProperty("视图类型为1或2的返回结果")
    private PastData pastData;

    @Getter
    @Setter
    @ApiModel("logsData")
    public static class LogsData {
        @ApiModelProperty("当天测试数据集合")
        private List<WandTestBiomarkerDTO> hormones = new ArrayList<>();

        @ApiModelProperty("弹窗展示的字符串")
        private String showWord;

        @ApiModelProperty("Note")
        private String notes;

        @ApiModelProperty("Sex")
        private String sex;

        @ApiModelProperty("温度默认单位，默认值与国际化对应")
        private String tempUnit;

        @ApiModelProperty("温度")
        private List<TemperatureDTO> temperatures = new ArrayList<>();

        @ApiModelProperty("体重")
        private BigDecimal weightValue;

        @ApiModelProperty("体重默认单位，默认值与国际化对应")
        private String weightUnit;

        @ApiModelProperty("身体状态列表")
        List<UserSymptomDTO> symptoms = new ArrayList<>();

        @ApiModelProperty("Mood & Well Being")
        UserDiaryMoodsDTO appUserDiaryMoodsVO;

        @ApiModelProperty("Cervical Mucus -- 白带状态(粘液状态)")
        private String mucusType;

        @ApiModelProperty("Cervical Mucus -- 白带流量")
        private String mucusFlow;

        @ApiModelProperty("验孕记录")
        private Boolean pregnant;

        @ApiModelProperty("排卵测试记录")
        private Boolean opk;

        @ApiModelProperty("出血状态")
        private String flowAndSpotting;

        @ApiModelProperty("子宫位置")
        private String cervicalPosition;

        @ApiModelProperty("Firmness")
        private String cervicalFirmness;

        @ApiModelProperty("Openness")
        private String cervicalOpenness;

        @ApiModelProperty("药物")
        private List<UserMedicineDTO> medications = new ArrayList<>();

        @ApiModelProperty("Challenge with glucose control")
        private String glucoseControl;
    }

    @Getter
    @Setter
    @ApiModel("pastData")
    public static class PastData {
        @ApiModelProperty("log数据")
        private LogsData logsData;

        @ApiModelProperty("当天测试数据集合")
        private List<WandTestBiomarkerDTO> hormones = new ArrayList<>();
    }
}
