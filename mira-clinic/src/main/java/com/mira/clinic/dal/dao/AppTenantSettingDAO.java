package com.mira.clinic.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.clinic.dal.entity.AppTenantSettingEntity;
import com.mira.clinic.dal.mapper.AppTenantSettingMapper;
import org.springframework.stereotype.Repository;

@Repository
public class AppTenantSettingDAO extends ServiceImpl<AppTenantSettingMapper, AppTenantSettingEntity> {
    public AppTenantSettingEntity getByTenantCode(String tenantCode) {
        return getOne(Wrappers.<AppTenantSettingEntity>lambdaQuery()
                .eq(AppTenantSettingEntity::getTenantCode, tenantCode));
    }
}
