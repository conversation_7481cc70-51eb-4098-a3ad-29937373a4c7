package com.mira.clinic.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.api.clinic.dto.DoctorPatientDTO;
import com.mira.clinic.dal.entity.AppTenantDoctorPatientEntity;
import com.mira.clinic.dal.mapper.AppTenantDoctorPatientMapper;
import com.mira.clinic.dto.TenantDoctorPatientListDTO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AppTenantDoctorPatientDAO extends ServiceImpl<AppTenantDoctorPatientMapper, AppTenantDoctorPatientEntity> {
    public List<AppTenantDoctorPatientEntity> listByPatientId(Long patientId) {
        return list(Wrappers.<AppTenantDoctorPatientEntity>lambdaQuery()
                .eq(AppTenantDoctorPatientEntity::getPatientId, patientId));
    }

    public List<TenantDoctorPatientListDTO> listTenantDoctorPatientListDTO(List<Long> patientIds) {
        return baseMapper.listTenantDoctorPatientListDTO(patientIds);
    }

    //todo
    public List<DoctorPatientDTO> listDoctorPatientDTO(Long userId) {
        return baseMapper.listDoctorPatientDTO(userId);
    }

    public void removeDoctorPatient(Long patientId) {
        baseMapper.removeDoctorPatient(patientId);
    }

    public void removeByDoctorId(Long doctorId) {
        baseMapper.removeByDoctorId(doctorId);
    }

    public void removeInDoctorPatient(Long patientId, List<Long> doctorIds) {
        baseMapper.removeInDoctorPatient(patientId, doctorIds);
    }
}
