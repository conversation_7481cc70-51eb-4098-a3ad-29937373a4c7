package com.mira.clinic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("TenantDoctorNoteDTO")
public class TenantDoctorNoteDTO {
    @ApiModelProperty("note id")
    private Long id;

    @ApiModelProperty("note内容")
    private String content;

    @ApiModelProperty("租户医生id")
    private Long doctorId;

    @ApiModelProperty("医生/护士名称")
    private String name;

    @ApiModelProperty("角色编码:2:医生;3:护士")
    private Integer role;

    @ApiModelProperty("创建时间")
    private String createTimeStr;
}
