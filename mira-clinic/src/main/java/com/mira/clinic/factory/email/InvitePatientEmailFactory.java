package com.mira.clinic.factory.email;

import com.mira.clinic.dto.TenantInfoDTO;
import com.mira.clinic.controller.dto.InvitePatientDTO;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.PasswordUtil;
import com.mira.core.util.RsaUtil;
import com.mira.core.util.StringUtil;
import com.mira.web.properties.RsaProperties;
import org.apache.commons.lang3.RandomUtils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 邀请Patient邮件模版工厂
 *
 * <AUTHOR>
 */
@SuppressWarnings("all")
public class InvitePatientEmailFactory implements EmailTemplateFactory<TenantInfoDTO> {
    private final RsaProperties rsaProperties;
    private final InvitePatientDTO invitePatientDTO;
    private final Long userId;
    private final Long patientId;

    public InvitePatientEmailFactory(RsaProperties rsaProperties, InvitePatientDTO invitePatientDTO, Long userId, Long patientId) {
        this.rsaProperties = rsaProperties;
        this.invitePatientDTO = invitePatientDTO;
        this.userId = userId;
        this.patientId = patientId;
    }

    @Override
    public Map<String, String> createTemplate(TenantInfoDTO tenantInfoDTO) {
        if (userId == null) {
            return notExistUser(tenantInfoDTO);
        } else {
            return existUser(tenantInfoDTO);
        }
    }

    private Map<String, String> notExistUser(TenantInfoDTO tenantInfoDTO) {
        // 邮件内链接加密数据
        Map<String, Object> encryptMap = new HashMap<>();
        encryptMap.put("patientId", patientId);
        encryptMap.put("patientInviteTimestamp", System.currentTimeMillis());
        encryptMap.put("userId", null);
        encryptMap.put("email", invitePatientDTO.getEmail());
        String salt = PasswordUtil.generateSalt(20);
        encryptMap.put("salt", salt);
        String randomPw = RandomUtils.nextInt(1, 9) + StringUtil.random(8);
        encryptMap.put("password", PasswordUtil.encryptPassword(randomPw, salt));
        // 邮件内变量
        String encryptStr = RsaUtil.encryptionRsa(JsonUtil.toJson(encryptMap), rsaProperties.getPublicKey());
        HashMap<String, String> variableMap = new HashMap<>();
        variableMap.put("userNickName", invitePatientDTO.getFirstName() + " " + invitePatientDTO.getLastName());
        variableMap.put("tenantName", tenantInfoDTO.getTenant().getName());
        variableMap.put("doctorName", tenantInfoDTO.getName());
        variableMap.put("hash", URLEncoder.encode(encryptStr, StandardCharsets.UTF_8));
        variableMap.put("type", "2");
        variableMap.put("password", randomPw);
        variableMap.put("userEmail", invitePatientDTO.getEmail());
        return variableMap;
    }

    private Map<String, String> existUser(TenantInfoDTO tenantInfoDTO) {
        // 邮件内链接加密数据
        Map<String, Object> encryptMap = new HashMap<>();
        encryptMap.put("patientId", patientId);
        encryptMap.put("patientInviteTimestamp", System.currentTimeMillis());
        encryptMap.put("userId", userId);
        encryptMap.put("email", invitePatientDTO.getEmail());
        // 邮件内变量
        String encryptStr = RsaUtil.encryptionRsa(JsonUtil.toJson(encryptMap), rsaProperties.getPublicKey());
        HashMap<String, String> variableMap = new HashMap<>();
        variableMap.put("userNickName", invitePatientDTO.getFirstName() + " " + invitePatientDTO.getLastName());
        variableMap.put("tenantName", tenantInfoDTO.getTenant().getName());
        variableMap.put("doctorName", tenantInfoDTO.getName());
        variableMap.put("hash", URLEncoder.encode(encryptStr, StandardCharsets.UTF_8));
        variableMap.put("type", "1");
        return variableMap;
    }

    public static Map<String, String> createTemplate(RsaProperties rsaProperties,
                                                     TenantInfoDTO tenantInfoDTO,
                                                     InvitePatientDTO invitePatientDTO,
                                                     Long userId,
                                                     Long patientId) {
        return new InvitePatientEmailFactory(rsaProperties, invitePatientDTO, userId, patientId).createTemplate(tenantInfoDTO);
    }
}
