create table quiz_question
(
    id                 bigint auto_increment comment '主键' primary key,
    next_id            bigint        null comment '下一个问题',
    prev_id            bigint        null comment '上一个问题',
    section            int           not null comment 'section',
    title              varchar(255)  not null comment '标题',
    answer_type        tinyint(4)    not null comment '问题回答类型',
    refer_user_profile boolean       not null default false comment '是否需要参考用户属性,默认为false',
    extra_str          varchar(4095) null comment '额外的数据',
    `order`            tinyint(4)    not null comment '排序',
    time_zone          varchar(100)  not null comment '时区',
    create_time_str    varchar(255)  not null comment '创建时间',
    sys_note           varchar(255)  null comment '系统备注'
)
    comment '调查问卷问题表';

create table quiz_answer
(
    id              bigint auto_increment comment '主键'
        primary key,
    user_id         bigint            not null comment '用户id',
    current_id      bigint            null comment '当前问题id',
    is_finished     tinyint default 0 not null default false comment '当前是否完成quiz问卷,默认false',
    answer          text              null comment '问题答案',
    `temporary`     text              null comment '临时数据',
    creator         bigint  default 0 not null,
    modifier        bigint  default 0 not null,
    deleted         tinyint default 0 not null,
    create_time     bigint            not null,
    modify_time     bigint            not null,
    create_time_str varchar(255)      not null,
    modify_time_str varchar(255)      not null,
    time_zone       varchar(100)      not null,
    sys_note        varchar(512)      null
)
    comment '调查问卷答案表';

create unique index user_id
    on quiz_answer (user_id);

create table quiz_early_access
(
    user_id         bigint            not null comment '用户id' primary key,
    email           varchar(100)      not null comment '用户邮箱',
    creator         bigint  default 0 not null,
    modifier        bigint  default 0 not null,
    deleted         tinyint default 0 not null,
    create_time     bigint            not null,
    modify_time     bigint            not null,
    create_time_str varchar(255)      not null,
    modify_time_str varchar(255)      not null,
    time_zone       varchar(100)      not null
)
    comment '调查问卷提前体验报名表';
