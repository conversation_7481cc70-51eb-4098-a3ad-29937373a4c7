alter table sys_home_banner
    add survey_ids_str varchar(512) null comment '关联的受影响的surveyId列表';

alter table app_user_info
    add bind_flag tinyint null comment '绑定标示：0没有绑定过；1绑定过;2:wait shipping';

alter table sys_home_banner
    add tracking_menopause tinyint null comment '是否跟踪更年期，null无标识；0不跟踪；1跟踪';

INSERT INTO `mira`.`sys_notification_define` (`define_id`, `title`, `icon`, `type`, `sub_type`, `language`,
                                              `content_type`, `content`, `style_type`,
                                              `background_image`, `background_color`, `button1`, `button2`,
                                              `button1_link`, `button2_link`, `extra1_link`, `extra2_link`,
                                              `picture_url`, `description`,
                                              `expire_time`, `enable`, `creator`, `modifier`, `deleted`, `time_zone`,
                                              `create_time`, `modify_time`, `create_time_str`, `modify_time_str`,
                                              `sys_note`)
VALUES (304, '', 'https://file.quanovate.com/picture/default/notification/202408/irregular_cycles.jpg',
        9, 1, 'en-us', 1, 'One month down, one month to go—see your Menopause stage!
You’ve been testing for 1 month, great job! Keep using your Mira Ovum Wands according to the schedule to see your Menopause Transition Stage plus personalized recommendations from us.',
        0,
        'https://file.quanovate.com/picture/default/notification/202408/irregular_cycles.jpg',
        '#FFFFFF', '', 'Great, thanks!', '', 'close', '', '',
        'https://file.quanovate.com/picture/default/notification/202408/irregular_cycles.jpg',
        'send the notification right after the stage percent became 60% in (menopause define stage:  0 && backend status:1 )',
        -1, 1, 0, 0, 0, 'Asia/Shanghai', 1726726233011, 1726726233011, '2024-09-19 14:10:33', '2024-09-19 14:10:33',
        '');



INSERT INTO `mira`.`sys_notification_define` (`define_id`, `title`, `icon`, `type`, `sub_type`, `language`,
                                              `content_type`, `content`, `style_type`,
                                              `background_image`, `background_color`, `button1`, `button2`,
                                              `button1_link`, `button2_link`, `extra1_link`, `extra2_link`,
                                              `picture_url`, `description`,
                                              `expire_time`, `enable`, `creator`, `modifier`, `deleted`, `time_zone`,
                                              `create_time`, `modify_time`, `create_time_str`, `modify_time_str`,
                                              `sys_note`)
VALUES (998, 'Start testing—see your Menopause stage',
        'https://file.quanovate.com/picture/default/notification/202409/start_testing_menopause_stage.jpg',
        9, 1, 'en-us', 1, '',
        1,
        'https://file.quanovate.com/picture/default/notification/202409/start_testing_menopause_stage.jpg',
        '#FFFFFF', '', 'Great, thanks!', '', 'close', null, null,
        'https://file.quanovate.com/picture/default/notification/202409/start_testing_menopause_stage.jpg',
        'send the notification if they haven''t started testing fsh in menopause mode)',
        -1, 1, 0, 0, 0, 'Asia/Shanghai', 1726726233011, 1726726233011, '2024-09-19 14:10:33', '2024-09-19 14:10:33',
        '');


INSERT INTO `mira`.`sys_notification_define` (`define_id`, `title`, `icon`, `type`, `sub_type`, `language`,
                                              `content_type`, `content`, `style_type`,
                                              `background_image`, `background_color`, `button1`, `button2`,
                                              `button1_link`, `button2_link`, `extra1_link`, `extra2_link`,
                                              `picture_url`, `description`,
                                              `expire_time`, `enable`, `creator`, `modifier`, `deleted`, `time_zone`,
                                              `create_time`, `modify_time`, `create_time_str`, `modify_time_str`,
                                              `sys_note`)
VALUES (999, 'Start testing—see your Menopause stage',
        'https://file.quanovate.com/picture/default/notification/202409/start_testing_menopause_stage_regular.jpg',
        9, 1, 'en-us', 1, '',
        1,
        'https://file.quanovate.com/picture/default/notification/202409/start_testing_menopause_stage_regular.jpg',
        '#FFFFFF', '', 'Great, thanks!', '', 'close', null, null,
        'https://file.quanovate.com/picture/default/notification/202409/start_testing_menopause_stage_regular.jpg',
        'send the notification if they haven''t started testing fsh in menopause mode)',
        -1, 1, 0, 0, 0, 'Asia/Shanghai', 1726726233011, 1726726233011, '2024-09-19 14:10:33', '2024-09-19 14:10:33',
        '');

-- 310 --> 998
-- 311 --> 999




