package com.mira.third.party.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("tp_oura_daily_sleep")
public class TpOuraDailySleepEntity {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long userId;

    private String dailyDay;
    private Integer deepSleep;
    private Integer efficiency;
    private Integer latency;
    private Integer remSleep;
    private Integer restfulness;
    private Integer timing;
    private Integer totalSleep;
    private Integer score;
    private String timestamp;
}
