package com.mira.third.party.client.oura;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.mira.third.party.dto.oura.OuraDailySleep;
import com.mira.third.party.dto.oura.OuraPersonalInfo;
import retrofit2.http.GET;
import retrofit2.http.Header;
import retrofit2.http.Query;

/**
 * Oura Ring
 *
 * <AUTHOR>
 */
@RetrofitClient(baseUrl = "https://api.ouraring.com", errorDecoder = OuraErrorDecoder.class, sourceOkHttpClient = "customSourceOkHttpClientRegistrar")
public interface OuraClient {
    @GET("/v2/usercollection/personal_info")
    OuraPersonalInfo personalInfo(@Header("Authorization") String Authorization);

    @GET("/v2/usercollection/daily_sleep")
    OuraDataResult<OuraDailySleep> dailySleep(@Header("Authorization") String Authorization, @Query("start_date") String startDate, @Query("end_date") String endDate);

    @GET("/v2/usercollection/sleep")
    Object sleep(@Header("Authorization") String Authorization, @Query("start_date") String startDate, @Query("end_date") String endDate);

    @GET("/v2/usercollection/daily_readiness")
    Object dailyReadiness(@Header("Authorization") String Authorization, @Query("start_date") String startDate, @Query("end_date") String endDate);

    @GET("/v2/usercollection/daily_activity")
    Object dailyActivity(@Header("Authorization") String Authorization, @Query("start_date") String startDate, @Query("end_date") String endDate);
}