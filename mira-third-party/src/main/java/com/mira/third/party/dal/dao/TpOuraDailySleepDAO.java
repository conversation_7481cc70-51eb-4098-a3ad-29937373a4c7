package com.mira.third.party.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.third.party.dal.entity.TpOuraDailySleepEntity;
import com.mira.third.party.dal.mapper.TpOuraDailySleepMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class TpOuraDailySleepDAO extends ServiceImpl<TpOuraDailySleepMapper, TpOuraDailySleepEntity> {
    public List<TpOuraDailySleepEntity> listByUserId(Long userId) {
        return list(Wrappers.<TpOuraDailySleepEntity>lambdaQuery()
                .eq(TpOuraDailySleepEntity::getUserId, userId));
    }
}
