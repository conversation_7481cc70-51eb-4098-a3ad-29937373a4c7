package com.mira.third.party.dto.oura;

import lombok.Getter;
import lombok.Setter;
import java.util.List;

@Getter
@Setter
public class OuraSleep {
    private String id;
    private Double average_breath;
    private Double average_heart_rate;
    private Integer average_hrv;
    private Integer awake_time;
    private String bedtime_end;
    private String bedtime_start;
    private String day;
    private Integer deep_sleep_duration;
    private Integer efficiency;
    private HeartRate heart_rate;
    private Hrv hrv;
    private Integer latency;
    private Integer light_sleep_duration;
    private Boolean low_battery_alert;
    private Integer lowest_heart_rate;
    private String movement_30_sec;
    private Integer period;
    private Readiness readiness;
    private Integer readiness_score_delta;
    private Integer rem_sleep_duration;
    private Integer restless_periods;
    private String sleep_phase_5_min;
    private Integer sleep_score_delta;
    private String sleep_algorithm_version;
    private Integer time_in_bed;
    private Integer total_sleep_duration;
    private String type;

    @Getter
    @Setter
    public static class HeartRate {
        private Integer interval;
        private List<Integer> items;
        private String timestamp;
    }

    @Getter
    @Setter
    public static class Hrv {
        private Integer interval;
        private List<Integer> items;
        private String timestamp;
    }

    @Getter
    @Setter
    public static class Readiness {
        private Contributors contributors;
        private Integer score;
        private Double temperature_deviation;
        private Double temperature_trend_deviation;

        @Getter
        @Setter
        public static class Contributors {
            private Integer activity_balance;
            private Integer body_temperature;
            private Integer hrv_balance;
            private Integer previous_day_activity;
            private Integer previous_night;
            private Integer recovery_index;
            private Integer resting_heart_rate;
            private Integer sleep_balance;
        }
    }
}
