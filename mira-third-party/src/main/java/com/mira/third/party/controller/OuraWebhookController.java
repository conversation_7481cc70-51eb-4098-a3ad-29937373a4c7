package com.mira.third.party.controller;

import com.mira.core.annotation.Anonymous;
import com.mira.core.annotation.NoPacking;
import com.mira.redis.cache.StringRedisComponent;
import com.mira.third.party.dto.oura.OuraWebhookDTO;
import com.mira.third.party.exception.ThirdPartyException;
import com.mira.third.party.service.IOuraService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * oura webhook controller
 *
 * <AUTHOR>
 */
@Api(tags = "Oura Webhook")
@RestController
public class OuraWebhookController {
    @Resource
    private IOuraService ouraService;

    @NoPacking
    @Anonymous
    @ApiOperation("校验订阅的Webhook")
    @GetMapping("/webhook/oura/subscription")
    public Map<String, String> verifySubscription(@RequestParam("verification_token") String verificationToken,
                                                  @RequestParam("challenge") String challenge) {
        String originVerificationToken = "";
        if (!verificationToken.equals(originVerificationToken)) {
            throw new ThirdPartyException("verification token is not match");
        }
        return Map.of("challenge", challenge);
    }

    @Anonymous
    @ApiOperation("更新数据")
    @PostMapping("/webhook/oura")
    public void webhook(@RequestBody OuraWebhookDTO ouraWebhookDTO) {
        ouraService.updateData(ouraWebhookDTO);
    }
}
