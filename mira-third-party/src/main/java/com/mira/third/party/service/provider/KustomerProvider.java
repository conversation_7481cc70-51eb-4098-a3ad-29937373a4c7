package com.mira.third.party.service.provider;

import com.mira.api.thirdparty.dto.kustomer.CreateCustomerDTO;
import com.mira.api.thirdparty.dto.kustomer.KustomerLoginDTO;
import com.mira.api.thirdparty.provider.IKustomerProvicer;
import com.mira.core.response.CommonResult;
import com.mira.third.party.client.kustomer.KustomerClient;
import com.mira.third.party.client.kustomer.KustomerResult;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * kustomer api 实现
 *
 * <AUTHOR>
 */
@RestController
public class KustomerProvider implements IKustomerProvicer {
    @Resource
    private KustomerClient kustomerClient;

    @Override
    public CommonResult<String> getToken(KustomerLoginDTO loginDTO) {
        KustomerResult kustomerResult = kustomerClient.getToken(loginDTO);
        return CommonResult.OK(kustomerResult.getData().getAttributes().getToken());
    }

    @Override
    public CommonResult<Object> getCustomerInfo(String authorization, String email) {
        Object result = kustomerClient.getCustomer("Bearer " + authorization, email);
        return CommonResult.OK(result);
    }

    @Override
    public CommonResult<Object> addCustomerInfo(String authorization, CreateCustomerDTO createCustomerDTO) {
        Object result = kustomerClient.createCustomer("Bearer " + authorization, createCustomerDTO);
        return CommonResult.OK(result);
    }

    @Override
    public CommonResult<Object> updateCustomInfo(String id, String authorization, CreateCustomerDTO updateCustomerDTO) {
        Object result = kustomerClient.updateCustomer("Bearer " + authorization, id, updateCustomerDTO);
        return CommonResult.OK(result);
    }

    @Override
    public CommonResult<Object> getChatMessages(String id, String authorization, String page, String pageSize) {
        Object result = kustomerClient.getCustomerMessages("Bearer " + authorization, id, page, pageSize);
        return CommonResult.OK(result);
    }
}
