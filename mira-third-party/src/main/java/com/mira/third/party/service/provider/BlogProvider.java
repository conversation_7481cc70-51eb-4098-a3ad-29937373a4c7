package com.mira.third.party.service.provider;

import com.mira.api.thirdparty.dto.blog.Ip2CountryDTO;
import com.mira.api.thirdparty.provider.IBlogProvider;
import com.mira.core.response.CommonResult;
import com.mira.third.party.service.IBlogService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Blog 接口实现类
 *
 * <AUTHOR>
 */
@RestController
public class BlogProvider implements IBlogProvider {
    @Resource
    private IBlogService blogService;

    @Override
    public CommonResult<Ip2CountryDTO> getCountryByIp(String ip, Long userId) {
        return CommonResult.OK(blogService.getCountryByIp(ip, userId));
    }

    @Override
    public CommonResult<Object> getBlogInfoList(List<Long> blogIdList) {
        return CommonResult.OK(blogService.getBlogInfoList(blogIdList));
    }

    @Override
    public CommonResult<Object> getFeaturedBlogList() {
        return CommonResult.OK(blogService.getFeaturedBlogList());
    }

    @Override
    public CommonResult<Object> searchBlog(String keyword, Integer page) {
        return CommonResult.OK(blogService.searchBlog(keyword, page));
    }

    @Override
    public CommonResult<Object> getTogether() {
        return CommonResult.OK(blogService.getTogether());
    }

    @Override
    public CommonResult<Object> getBlogDetail(Long blogId) {
        return CommonResult.OK(blogService.getBlogDetail(blogId));
    }

    @Override
    public CommonResult<Object> getTermBlogList(String term, Integer page) {
        return CommonResult.OK(blogService.getTermBlogList(term, page));
    }

    @Override
    public CommonResult<Object> getTypeBlogList(String type, Integer page) {
        return CommonResult.OK(blogService.getTypeBlogList(type, page));
    }

    @Override
    public CommonResult<Object> product(String name, String currency) {
        return CommonResult.OK(blogService.product(name, currency));
    }

    @Override
    public CommonResult<Object> course() {
        return CommonResult.OK(blogService.course());
    }

    @Override
    public CommonResult<Object> chapter(Long chapterId) {
        return CommonResult.OK(blogService.chapter(chapterId));
    }

    @Override
    public CommonResult<Object> webinarAll() {
        return CommonResult.OK(blogService.webinarAll());
    }
}
