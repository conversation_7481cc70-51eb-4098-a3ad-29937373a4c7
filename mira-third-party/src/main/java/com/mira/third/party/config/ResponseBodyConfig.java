package com.mira.third.party.config;

import com.mira.core.annotation.NoPacking;
import com.mira.core.response.CommonResult;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * 响应统一处理
 *
 * <AUTHOR>
 */
@RestControllerAdvice("com.mira.third.party")
public class ResponseBodyConfig implements ResponseBodyAdvice {
    @Override
    public boolean supports(MethodParameter returnType, Class converterType) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class selectedConverterType,
                                  ServerHttpRequest request, ServerHttpResponse response) {
        if (returnType.hasMethodAnnotation(NoPacking.class)) {
            return body;
        }

        if (body instanceof CommonResult) {
            return body;
        }

        return CommonResult.OK(body);
    }
}
