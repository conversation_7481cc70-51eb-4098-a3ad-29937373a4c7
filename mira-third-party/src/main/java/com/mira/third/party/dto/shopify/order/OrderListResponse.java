package com.mira.third.party.dto.shopify.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-03-27
 **/
@Getter
@Setter
@ApiModel("orders对象")
public class OrderListResponse {
    @ApiModelProperty(value = "orders对象")
    private List<Order> orders;

}
