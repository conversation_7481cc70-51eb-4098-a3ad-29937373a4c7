package com.mira.third.party.exception;

import com.mira.core.response.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;

/**
 * 三方服务异常处理器
 *
 * <AUTHOR>
 */
@Slf4j
@ResponseBody
@RestControllerAdvice
public class ThirdPartyExceptionHandler implements Ordered {
    @ExceptionHandler(ThirdPartyException.class)
    public CommonResult<String> thirdPartyExHandler(HttpServletRequest request, ThirdPartyException ex) {
        String url = request.getRequestURL().toString();
        log.error("url:{}, code:{}, msg:{}", url, ex.getCode(), ex.getMsg());
        return CommonResult.FAILED(ex.getCode(), ex.getMsg());
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(XperiencifyException.class)
    public Object handleXperiencifyException(XperiencifyException ex) {
        CommonResult<Object> commonResult = CommonResult.OK(ex.getMessage());
        commonResult.setMsg("fail");
        return commonResult;
    }

    @Override
    public int getOrder() {
        return Integer.MIN_VALUE + 8;
    }
}
