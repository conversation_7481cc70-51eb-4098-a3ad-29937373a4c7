package com.mira.third.party.service.impl;

import com.mira.core.util.RsaUtil;
import com.mira.redis.cache.RedisComponent;
import com.mira.third.party.client.oura.OuraClient;
import com.mira.third.party.client.oura.OuraOauthClient;
import com.mira.third.party.client.oura.OuraOauthConst;
import com.mira.third.party.client.oura.OuraTokenResult;
import com.mira.third.party.controller.vo.oura.OuraDataVO;
import com.mira.third.party.dto.oura.OuraPersonalInfo;
import com.mira.third.party.dto.oura.OuraWebhookDTO;
import com.mira.third.party.service.IOuraService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * oura service impl
 *
 * <AUTHOR>
 */
@Service
public class OuraServiceImpl implements IOuraService {
    @Resource
    private OuraClient ouraClient;
    @Resource
    private OuraOauthClient ouraOauthClient;
    @Resource
    private RedisComponent redisComponent;

    private final String OURA_TOKEN_CACHE_KEY = "oura:token:";

    @Override
    public void initUserData(String code, String state) {
        // 解析state，获取mira用户id
        String userId = RsaUtil.decodeRsa(state, OuraOauthConst.PRIVATE_KEY);

        // 根据code获取token，存储token
        OuraTokenResult ouraTokenResult = ouraOauthClient.token("authorization_code", code, OuraOauthConst.CLIENT_ID, OuraOauthConst.CLIENT_SECRET);
        String accessToken = ouraTokenResult.getAccess_token();
        Long expireTime = ouraTokenResult.getExpires_in();
        redisComponent.setEx(OURA_TOKEN_CACHE_KEY + userId, accessToken, expireTime, TimeUnit.SECONDS);

        // 根据token获取用户信息
        OuraPersonalInfo ouraPersonalInfo = ouraClient.personalInfo(accessToken);

        // 拉取oura用户所有相关数据（近3个月），并入库
    }

    @Override
    public void updateData(OuraWebhookDTO ouraWebhookDTO) {
        // 根据oura user id查找到mira user关联信息，以及存储数据的表

        // 根据webhook数据类型，更新数据
    }

    @Override
    public OuraDataVO getOuraData() {
        // 获取mira user id

        // 查找对应的oura数据表

        return new OuraDataVO();
    }
}
