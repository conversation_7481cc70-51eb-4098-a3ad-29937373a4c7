package com.mira.mongo.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.mongo.dto.*;
import com.mira.mongo.domain.WandsExceptionAlarm;
import com.mira.mongo.repository.WandsExceptionAlarmRepository;
import com.mira.mongo.service.IWandsExceptionAlarmService;
import com.mira.mongo.util.DomainTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.ChangeStreamEvent;
import org.springframework.data.mongodb.core.ChangeStreamOptions;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-01-29
 **/
@Slf4j
@Service
public class WandsExceptionAlarmServiceImpl implements IWandsExceptionAlarmService {
    @Resource
    private WandsExceptionAlarmRepository wandsExceptionAlarmRepository;

    @Resource
    private ReactiveMongoTemplate reactiveMongoTemplate;


    @Override
    public void saveOrUpdate(WandsExceptionAlarmDTO wandsExceptionAlarmDTO) {
        Long userId = wandsExceptionAlarmDTO.getUserId();
        String timeZone = wandsExceptionAlarmDTO.getTimeZone();
        WandsExceptionAlarm wandsExceptionAlarm = wandsExceptionAlarmRepository.findByUserId(userId);
        if (wandsExceptionAlarm == null) {
            wandsExceptionAlarm = new WandsExceptionAlarm();
            wandsExceptionAlarm.setUserId(userId);
            wandsExceptionAlarm.setEmail(wandsExceptionAlarmDTO.getEmail());
            WandsExceptionAlarmContentDTO wandsExceptionAlarmContentDTO = new WandsExceptionAlarmContentDTO();
            BeanUtil.copyProperties(wandsExceptionAlarmDTO, wandsExceptionAlarmContentDTO);
            LinkedList<WandsExceptionAlarmContentDTO> alarmContents = new LinkedList<>();
            alarmContents.add(wandsExceptionAlarmContentDTO);
            wandsExceptionAlarm.setAlarmContents(alarmContents);
            DomainTimeUtil.setEntityTime(timeZone, wandsExceptionAlarm);
            wandsExceptionAlarmRepository.save(wandsExceptionAlarm);
        } else {
            LinkedList<WandsExceptionAlarmContentDTO> alarmContents = wandsExceptionAlarm.getAlarmContents();
            WandsExceptionAlarmContentDTO wandsExceptionAlarmContentDTO = new WandsExceptionAlarmContentDTO();
            BeanUtil.copyProperties(wandsExceptionAlarmDTO, wandsExceptionAlarmContentDTO);
            alarmContents.addFirst(wandsExceptionAlarmContentDTO);
            wandsExceptionAlarm.setAlarmContents(alarmContents);
            DomainTimeUtil.updateEntityTime(timeZone, wandsExceptionAlarm);
            wandsExceptionAlarmRepository.save(wandsExceptionAlarm);
        }

    }

    @Override
    public WandsExceptionAlarmPageVO wandsExceptionAlarmPage(WandsExceptionAlarmPageRequestDTO wandsExceptionAlarmPageRequestDTO) {
        String email = wandsExceptionAlarmPageRequestDTO.getEmail();
        Integer current = wandsExceptionAlarmPageRequestDTO.getCurrent();
        Integer size = wandsExceptionAlarmPageRequestDTO.getSize();

        // 创建分页请求对象，按modifyTime降序排序
        Pageable pageable = PageRequest.of(current - 1, size, Sort.by(Sort.Direction.DESC, "modifyTime"));

        // 执行查询
        Page<WandsExceptionAlarm> page = wandsExceptionAlarmRepository.findByEmailRegex(Optional.ofNullable(email),
                pageable);
        WandsExceptionAlarmPageVO wandsExceptionAlarmPageVO = new WandsExceptionAlarmPageVO();
        wandsExceptionAlarmPageVO.setTotal(page.getTotalElements());
        // 将实体转换为VO对象
        List<WandsExceptionAlarmVO> wandsExceptionAlarmVOS = page.getContent().stream()
                                                                 .map(this::convertToAlarmVO)
                                                                 .collect(Collectors.toList());
        wandsExceptionAlarmPageVO.setWandsExceptionAlarmVOS(wandsExceptionAlarmVOS);
        // 返回分页结果
        return wandsExceptionAlarmPageVO;

    }

    // 实体到VO的转换方法
    private WandsExceptionAlarmVO convertToAlarmVO(WandsExceptionAlarm alarm) {
        WandsExceptionAlarmVO vo = new WandsExceptionAlarmVO();
        BeanUtil.copyProperties(alarm, vo);
        return vo;
    }

    @Override
    public Flux<ChangeStreamEvent<WandsExceptionAlarm>> streamAlarms() {
        // Specify that we only want to receive the full document for insert operations
        ChangeStreamOptions changeStreamOptions =
                ChangeStreamOptions.builder()
                                   .filter(newAggregation(
                                                   Aggregation.match(
                                                           Criteria.where("operationType")
                                                                   //.in("insert", "update", "delete")
                                                                   .in("insert", "update")
                                                   )
                                           )
                                   )
                                   .returnFullDocumentOnUpdate() // Only needed for update operations but included here for completeness
                                   .build();

        // Return the change stream flux
        return reactiveMongoTemplate.changeStream("user_wands_exception_alarm", changeStreamOptions,
                WandsExceptionAlarm.class);
    }
}
