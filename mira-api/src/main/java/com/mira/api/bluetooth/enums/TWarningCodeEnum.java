package com.mira.api.bluetooth.enums;

import lombok.Getter;

/**
 * T线警告code
 */
@Getter
public enum TWarningCodeEnum {
    CODE_0("00", "初始代码，无警告"),
    CODE_1("01", "Area larger than top"),
    CODE_2("02", "concentration larger than upper limit"),
    CODE_3("03", "concentration smaller than lower limit"),
    CODE_4("04", "Area smaller than bottom"),
    ;

    private final String value;
    private final String description;

    TWarningCodeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static TWarningCodeEnum getEnumByValue(String value) {
        for (TWarningCodeEnum codeEnum : values()) {
            if (codeEnum.getValue().equals(value)) {
                return codeEnum;
            }
        }
        return null;
    }
}
