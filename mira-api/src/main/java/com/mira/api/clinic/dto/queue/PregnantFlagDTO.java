package com.mira.api.clinic.dto.queue;

import com.mira.api.clinic.enums.PregnantFlagEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("怀孕标记")
public class PregnantFlagDTO {
    @ApiModelProperty("用户编号")
    private Long userId;

    @ApiModelProperty("时区")
    private String timeZone;

    @ApiModelProperty("怀孕标记")
    private PregnantFlagEnum pregnantFlagEnum;
}
