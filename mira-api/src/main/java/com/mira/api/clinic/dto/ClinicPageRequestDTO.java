package com.mira.api.clinic.dto;

import com.mira.core.request.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 诊所信息分页请求
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-08-28
 **/
@Getter
@Setter
@ApiModel(description = "诊所信息分页请求")
public class ClinicPageRequestDTO extends PageDTO {
    @ApiModelProperty("查询参数")
    private String keyword;

    @ApiModelProperty("banner group uid")
    private String bannerGroupUid;

    @ApiModelProperty("notification task id")
    private String taskId;
}
