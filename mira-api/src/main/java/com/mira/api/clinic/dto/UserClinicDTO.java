package com.mira.api.clinic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("UserClinicDTO")
public class UserClinicDTO {
    @ApiModelProperty("租户id")
    private Long id;

    @ApiModelProperty("租户code")
    private String code;

    @ApiModelProperty("租户名称")
    private String name;

    @ApiModelProperty("租户图标")
    private String icon;

    @ApiModelProperty("病人账号状态:1:邀请中;2:正常激活状态;")
    private Integer status;

    @ApiModelProperty("绑定时间")
    private Long bindTime;

    @ApiModelProperty("邀请时间")
    private Long inviteTime;
}
