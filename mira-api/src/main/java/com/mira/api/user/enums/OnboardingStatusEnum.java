package com.mira.api.user.enums;

import lombok.Getter;

/**
 * onboarding status
 *
 * <AUTHOR>
 */
@Getter
public enum OnboardingStatusEnum {
    UNFINISHED(0, "未完成"),
    PAIRED_DEVICE(1, "到连接仪器"),
    HOME_PAGE(2, "到达首页");

    private final Integer code;
    private final String description;

    OnboardingStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
}
