package com.mira.api.user.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("用户保存DTO")
public class AppUserSaveDTO {
    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("密码等级")
    private Integer passwordGrade;

    @ApiModelProperty("盐")
    private String salt;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("来源")
    private Integer source;

    @ApiModelProperty("first name")
    private String firstName;

    @ApiModelProperty("last name")
    private String lastName;

    @ApiModelProperty("昵称")
    private String nickname;
}
