package com.mira.api.user.dto.user.diary;

import com.mira.api.user.dto.user.TemperatureDTO;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ApiModel("用户日记记录整合")
public class UserDiaryIntegrationDTO {
    private UserDiaryDTO userDiaryDTO;
    private List<UserSymptomDTO> userSymptomDTOS;
    private List<UserMedicineDTO> userMedicineDTOS;
    private UserDiaryMoodsDTO userDiaryMoodsDTO;
    private List<TemperatureDTO> temperatureDTOS;
}
