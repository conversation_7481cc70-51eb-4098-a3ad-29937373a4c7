package com.mira.api.user.dto.user.diary.excel;

import com.mira.api.user.dto.user.diary.UserMedicineDTO;
import com.mira.api.user.dto.user.diary.UserSymptomDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-09-02
 **/
@Getter
@Setter
@ApiModel("导出用户Symptom")
public class ExportUserSymptomDTO {

    @ApiModelProperty("symptom记录")
    private List<UserSymptomDTO> userSymptoms = new ArrayList<>();

    @ApiModelProperty("创建时间")
    private String createTimeStr;
}
