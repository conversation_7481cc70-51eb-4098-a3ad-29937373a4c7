package com.mira.api.user.enums.daily;

import lombok.Getter;

/**
 * Period Other
 */
@Getter
public enum DailyStatusPeriodOtherEnum {
    MB("mb", "Miscarriage bledding"),
    PB("pb", "Postpartum bleeding");

    private final String value;
    private final String description;

    DailyStatusPeriodOtherEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }
}
