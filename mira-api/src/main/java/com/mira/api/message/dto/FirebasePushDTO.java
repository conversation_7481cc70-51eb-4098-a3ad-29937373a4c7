package com.mira.api.message.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;
import java.util.Objects;

@Getter
@Setter
@ApiModel("firebase推送对象")
public class FirebasePushDTO {
    @ApiModelProperty("defineId")
    private Long defineId;

    @ApiModelProperty("title")
    private String title;

    @ApiModelProperty("body")
    private String body;

    @ApiModelProperty("image")
    private String image;

    @ApiModelProperty("dataMap")
    private Map<String, String> dataMap;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FirebasePushDTO that = (FirebasePushDTO) o;
        return Objects.equals(defineId, that.defineId) && Objects.equals(dataMap, that.dataMap);
    }

    @Override
    public int hashCode() {
        return Objects.hash(defineId, dataMap);
    }
}
