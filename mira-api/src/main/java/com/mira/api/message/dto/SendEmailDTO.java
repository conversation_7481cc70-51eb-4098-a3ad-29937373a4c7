package com.mira.api.message.dto;

import com.mira.core.consts.enums.UserTypeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 邮件发送信息传输类
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class SendEmailDTO<T> {
    /**
     * @see UserTypeEnum
     */
    private String userType;

    private T emailDTO;

    /**
     * 增加这个字段，为了对userType做更细粒度区分
     * userType为clinic情况下，空或者0表示系统发送，1表示发送给诊所doctor账号
     */
    private Integer type;
}
