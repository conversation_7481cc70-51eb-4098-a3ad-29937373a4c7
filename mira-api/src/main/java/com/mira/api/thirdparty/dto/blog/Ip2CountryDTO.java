package com.mira.api.thirdparty.dto.blog;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 根据 ip 获取国家编号
 */
@Getter
@Setter
@ApiModel("国家编号")
public class Ip2CountryDTO {
    @ApiModelProperty("ip")
    private String ip;

    @ApiModelProperty("洲编号")
    private String continentCode;

    @ApiModelProperty("国家编号")
    private String countryCode;

    @ApiModelProperty("国家名称")
    private String countryName;

    @ApiModelProperty("地区名称")
    private String regionName;

    @ApiModelProperty("城市名称")
    private String cityName;
}
