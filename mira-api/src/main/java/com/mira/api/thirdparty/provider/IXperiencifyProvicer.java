package com.mira.api.thirdparty.provider;

import com.mira.api.thirdparty.consts.path.XperiencifyApiConst;
import com.mira.api.thirdparty.dto.xperiencify.XperiencifyDTO;
import com.mira.core.response.CommonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * xperiencify 接口
 *
 * <AUTHOR>
 */
@FeignClient(name = "mira-third-party", contextId = "xperiencify")
public interface IXperiencifyProvicer {
    /**
     * 获取token
     *
     * @param xperiencifyDTO 请求参数
     * @return Object
     */
    @PostMapping(XperiencifyApiConst.TOKEN)
    CommonResult<Object> getToken(@RequestBody XperiencifyDTO xperiencifyDTO);

    /**
     * 获取学生信息
     *
     * @param student_id    学生id
     * @param authorization token
     * @param site_id       站点id
     * @return Object
     */
    @GetMapping(XperiencifyApiConst.STUDENT_INFO)
    CommonResult<Object> getStudentInfo(@PathVariable("student_id") String student_id,
                                        @RequestParam("authorization") String authorization,
                                        @RequestParam("site_id") String site_id);

    /**
     * 获取所有学生信息
     *
     * @param authorization token
     * @param site_id       站点id
     * @return Object
     */
    @GetMapping(XperiencifyApiConst.STUDENT_ALL)
    CommonResult<Object> getAllStudent(@RequestParam("authorization") String authorization,
                                       @RequestParam("site_id") String site_id);

    /**
     * 获取登录信息
     *
     * @param authorization token
     * @return Object
     */
    @GetMapping(XperiencifyApiConst.LOGIN_INFO)
    CommonResult<Object> getLoginInfo(@RequestParam("authorization") String authorization);
}
