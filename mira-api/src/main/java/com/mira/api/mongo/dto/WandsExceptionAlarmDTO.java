package com.mira.api.mongo.dto;

import com.mira.api.bluetooth.enums.DeviceErrorCodeEnum;
import com.mira.api.mongo.enums.WandsExceptionAlarmEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-01-29
 **/
@Getter
@Setter
@ApiModel("试剂异常告警")
public class WandsExceptionAlarmDTO {
    private Long userId;
    private String timeZone;
    @ApiModelProperty("邮箱")
    private String email;
    /**
     * @see WandsExceptionAlarmEnum
     */
    @ApiModelProperty("告警码")
    private String alarmCode;
    @ApiModelProperty("告警触发时间")
    private String triggerTime;
    @ApiModelProperty("试剂测试时间")
    private String wandTestTime;
    @ApiModelProperty("试剂类型，参考WandTypeEnum")
    private Integer wandType;
    /**
     * @see DeviceErrorCodeEnum
     */
    @ApiModelProperty("Warning/Error值")
    private String ecode;
    @ApiModelProperty("告警详细信息")
    private String detail;
}
