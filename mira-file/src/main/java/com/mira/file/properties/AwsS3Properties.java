package com.mira.file.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * aws s3 配置
 *
 * <AUTHOR>
 */
@Setter
@Getter
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "aws.s3")
public class AwsS3Properties {
    /**
     * id，唯一标识
     */
    private String accessKey;

    /**
     * 密钥
     */
    private String secretKey;

    /**
     * 桶名称
     */
    private String bucket;

    /**
     * 服务地址
     */
    private String endpoint;

    /**
     * 区域
     */
    private String region;

    /**
     * 阿里云 OSS 支持
     */
    private Boolean pathStyle;

    /**
     * 失败重试次数
     */
    private Integer maxErrorRetry;
}
