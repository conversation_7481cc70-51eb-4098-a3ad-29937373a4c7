package com.mira.file.service.provider;

import com.mira.api.file.provider.IFileProvider;
import com.mira.core.response.CommonResult;
import com.mira.core.util.LocalDateUtil;
import com.mira.file.dal.dao.SysOssDAO;
import com.mira.file.dal.entity.SysOssEntity;
import com.mira.file.properties.AwsS3Properties;
import com.mira.file.service.AwsS3Service;
import com.mira.web.properties.SysDictProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;

/**
 * 文件操作接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class FileProvier implements IFileProvider {
    @Resource
    private SysOssDAO sysOssDAO;

    @Resource
    private AwsS3Service awsS3Service;
    @Resource
    private AwsS3Properties awsS3Properties;
    @Resource
    private SysDictProperties sysDictProperties;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult<String> upload(MultipartFile file, String type) {
        try {
            String suffix = Objects.requireNonNull(file.getOriginalFilename()).substring(file.getOriginalFilename().lastIndexOf("."));
            String filePath = getPath(getPrefix(type), suffix);
            String url = awsS3Service.putObject(awsS3Properties.getBucket(), filePath, file);
            String finalUrl = url.replace(sysDictProperties.getPicturePrefix(), sysDictProperties.getPictureReplacePrefix());
            // save log
            SysOssEntity ossEntity = new SysOssEntity();
            ossEntity.setType(type);
            ossEntity.setUrl(finalUrl);
            ossEntity.setCreateDate(new Date());
            sysOssDAO.save(ossEntity);

            log.info("upload file, type:{}，url:{},finalUrl:{}", type, url, finalUrl);
            // return
            return CommonResult.OK(finalUrl);

        } catch (Exception e) {
            log.error("upload fail", e);
        }
        return CommonResult.OK();
    }

    /**
     * 文件路径
     *
     * @param prefix 前缀
     * @param suffix 后缀
     * @return 返回上传路径
     */
    private String getPath(String prefix, String suffix) {
        //生成uuid
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        //文件路径
        String path = LocalDateUtil.format(LocalDate.now(), "yyyyMMdd") + "/" + uuid;

        if (StringUtils.isNotBlank(prefix)) {
            path = prefix + "/" + path;
        }

        return path + suffix;
    }

    /**
     * 获取路径前缀
     *
     * @param type 文件类型
     * @return 路径前缀
     */
    private String getPrefix(String type) {
        if ("avatar".equals(type)) {
            return "user/avatar";
        } else if ("share".equals(type)) {
            return "user/share";
        } else if ("manual-data".equals(type)) {
            return "user/manual-data";
        } else if ("clinic".equals(type)) {
            return "clinic";
        }
        return "default/" + type;
    }
}
